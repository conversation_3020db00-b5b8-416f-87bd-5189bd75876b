"""
Daily Production Scheduling MIP Implementation using PuLP

This module implements the mathematical formulation from 01_MIP実装.ipynb
for daily production scheduling optimization.

Mathematical Formulation:
- Objective: Maximize profit (revenue - costs)
- Costs include: inventory cost, production cost, setup cost, opportunity loss cost
- Constraints: production time, setup limits, inventory balance, opportunity loss
"""

import pulp
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional


class ProductionScheduler:
    """
    Daily Production Scheduling MIP Solver using PuLP
    
    Parameters:
    - m: number of products
    - n: number of time periods
    - v_i: unit inventory cost for product i
    - c_i: unit production cost for product i  
    - s_i: unit setup cost for product i
    - h_i: unit opportunity loss cost for product i
    - d_it: demand for product i in period t
    - r_it: unit revenue for product i in period t
    - a_i: production time per unit for product i
    - b_i: setup time for product i
    - T: maximum load time per period
    - U: maximum number of setups per period
    - epsilon: minimum production quantity constant
    - zeta: maximum production quantity constant
    """
    
    def __init__(self, 
                 m: int, 
                 n: int,
                 v_i: List[float],
                 c_i: List[float], 
                 s_i: List[float],
                 h_i: List[float],
                 d_it: np.ndarray,
                 r_it: np.ndarray,
                 a_i: List[float],
                 b_i: List[float],
                 T: float,
                 U: int,
                 epsilon: float = 1.0,
                 zeta: float = 1000.0,
                 initial_inventory: Optional[List[float]] = None):
        
        self.m = m  # number of products
        self.n = n  # number of periods
        
        # Cost parameters
        self.v_i = v_i  # unit inventory cost
        self.c_i = c_i  # unit production cost
        self.s_i = s_i  # unit setup cost
        self.h_i = h_i  # unit opportunity loss cost
        
        # Demand and revenue
        self.d_it = d_it  # demand[i][t]
        self.r_it = r_it  # revenue[i][t]
        
        # Production parameters
        self.a_i = a_i  # production time per unit
        self.b_i = b_i  # setup time
        
        # Capacity constraints
        self.T = T      # maximum load time
        self.U = U      # maximum setups
        
        # Production bounds
        self.epsilon = epsilon  # minimum production
        self.zeta = zeta       # maximum production
        
        # Initial inventory
        self.initial_inventory = initial_inventory or [0.0] * m
        
        # Initialize problem
        self.prob = None
        self.x_it = {}  # setup variables
        self.p_it = {}  # production variables
        self.I_it = {}  # inventory variables
        self.g_it = {}  # opportunity loss variables
        
    def build_model(self):
        """Build the MIP model"""
        
        # Create the problem
        self.prob = pulp.LpProblem("Production_Scheduling", pulp.LpMaximize)
        
        # Decision variables
        # x_it: binary setup variable
        self.x_it = pulp.LpVariable.dicts("setup", 
                                         [(i, t) for i in range(self.m) for t in range(self.n)],
                                         cat='Binary')
        
        # p_it: production quantity
        self.p_it = pulp.LpVariable.dicts("production",
                                         [(i, t) for i in range(self.m) for t in range(self.n)],
                                         lowBound=0, cat='Continuous')
        
        # I_it: inventory level
        self.I_it = pulp.LpVariable.dicts("inventory",
                                         [(i, t) for i in range(self.m) for t in range(self.n)],
                                         lowBound=0, cat='Continuous')
        
        # g_it: opportunity loss
        self.g_it = pulp.LpVariable.dicts("opportunity_loss",
                                         [(i, t) for i in range(self.m) for t in range(self.n)],
                                         lowBound=0, cat='Continuous')
        
        # Objective function: maximize profit
        revenue = pulp.lpSum([self.d_it[i][t] * self.r_it[i][t] 
                             for i in range(self.m) for t in range(self.n)])
        
        inventory_cost = pulp.lpSum([self.v_i[i] * self.I_it[(i, t)]
                                    for i in range(self.m) for t in range(self.n)])
        
        production_cost = pulp.lpSum([self.c_i[i] * self.p_it[(i, t)]
                                     for i in range(self.m) for t in range(self.n)])
        
        setup_cost = pulp.lpSum([self.s_i[i] * self.x_it[(i, t)]
                                for i in range(self.m) for t in range(self.n)])
        
        opportunity_cost = pulp.lpSum([self.h_i[i] * self.g_it[(i, t)]
                                      for i in range(self.m) for t in range(self.n)])
        
        self.prob += revenue - inventory_cost - production_cost - setup_cost - opportunity_cost
        
        # Constraints
        self._add_constraints()
        
    def _add_constraints(self):
        """Add all constraints to the model"""
        
        # (1) Total production time + setup time <= maximum load time
        for t in range(self.n):
            self.prob += (pulp.lpSum([self.a_i[i] * self.p_it[(i, t)] for i in range(self.m)]) +
                         pulp.lpSum([self.b_i[i] * self.x_it[(i, t)] for i in range(self.m)])) <= self.T
        
        # (2) Total number of setups <= maximum setups
        for t in range(self.n):
            self.prob += pulp.lpSum([self.x_it[(i, t)] for i in range(self.m)]) <= self.U
        
        # (3) Inventory balance equation
        for i in range(self.m):
            for t in range(self.n):
                if t == 0:
                    # First period: I_it = initial_inventory + p_it - d_it
                    self.prob += (self.I_it[(i, t)] == 
                                 self.initial_inventory[i] + self.p_it[(i, t)] - self.d_it[i][t])
                else:
                    # Other periods: I_it = I_i(t-1) + p_it - d_it
                    self.prob += (self.I_it[(i, t)] == 
                                 self.I_it[(i, t-1)] + self.p_it[(i, t)] - self.d_it[i][t])
        
        # (4) Opportunity loss constraint: g_it >= d_it - I_it
        for i in range(self.m):
            for t in range(self.n):
                self.prob += self.g_it[(i, t)] >= self.d_it[i][t] - self.I_it[(i, t)]
        
        # (5) Production bounds with setup constraint
        for i in range(self.m):
            for t in range(self.n):
                # Minimum production when setup
                self.prob += self.p_it[(i, t)] >= self.epsilon * self.x_it[(i, t)]
                # Maximum production when setup
                self.prob += self.p_it[(i, t)] <= self.zeta * self.x_it[(i, t)]
    
    def solve(self, solver=None, time_limit=None, verbose=True):
        """
        Solve the MIP problem
        
        Args:
            solver: PuLP solver to use (default: PULP_CBC_CMD)
            time_limit: Time limit in seconds
            verbose: Whether to print solver output
            
        Returns:
            status: Solution status
        """
        if self.prob is None:
            self.build_model()
        
        if solver is None:
            solver = pulp.PULP_CBC_CMD(msg=verbose, timeLimit=time_limit)
        
        self.prob.solve(solver)
        
        return pulp.LpStatus[self.prob.status]
    
    def get_solution(self) -> Dict:
        """
        Extract solution from solved model
        
        Returns:
            Dictionary containing solution data
        """
        if self.prob.status != pulp.LpStatusOptimal:
            return {"status": "Not optimal", "objective": None}
        
        solution = {
            "status": "Optimal",
            "objective_value": pulp.value(self.prob.objective),
            "setup_schedule": np.zeros((self.m, self.n)),
            "production_schedule": np.zeros((self.m, self.n)),
            "inventory_levels": np.zeros((self.m, self.n)),
            "opportunity_losses": np.zeros((self.m, self.n))
        }
        
        # Extract variable values
        for i in range(self.m):
            for t in range(self.n):
                solution["setup_schedule"][i][t] = pulp.value(self.x_it[(i, t)])
                solution["production_schedule"][i][t] = pulp.value(self.p_it[(i, t)])
                solution["inventory_levels"][i][t] = pulp.value(self.I_it[(i, t)])
                solution["opportunity_losses"][i][t] = pulp.value(self.g_it[(i, t)])
        
        return solution
    
    def print_solution(self):
        """Print formatted solution"""
        solution = self.get_solution()
        
        if solution["status"] != "Optimal":
            print(f"Solution status: {solution['status']}")
            return
        
        print(f"Optimal objective value: {solution['objective_value']:.2f}")
        print("\nSetup Schedule:")
        print(pd.DataFrame(solution["setup_schedule"], 
                          columns=[f"Period_{t+1}" for t in range(self.n)],
                          index=[f"Product_{i+1}" for i in range(self.m)]))
        
        print("\nProduction Schedule:")
        print(pd.DataFrame(solution["production_schedule"], 
                          columns=[f"Period_{t+1}" for t in range(self.n)],
                          index=[f"Product_{i+1}" for i in range(self.m)]))
        
        print("\nInventory Levels:")
        print(pd.DataFrame(solution["inventory_levels"], 
                          columns=[f"Period_{t+1}" for t in range(self.n)],
                          index=[f"Product_{i+1}" for i in range(self.m)]))


def load_data_from_csv(csv_file: str,
                      n_periods: int = 4,
                      unit_inventory_cost: float = 1.0,
                      unit_production_cost: float = 10.0,
                      unit_setup_cost: float = 50.0,
                      unit_opportunity_cost: float = 100.0,
                      unit_revenue: float = 25.0,
                      setup_time: float = 30.0,
                      max_load_time: float = 1200.0,  # 8+2 hours * 60 min * 2 shifts
                      max_setups: int = 6) -> ProductionScheduler:
    """
    Load production data from CSV file and create ProductionScheduler

    Args:
        csv_file: Path to CSV file with columns: part_number, shipment, capacity, cycle_time, cabity, initial_inventory
        n_periods: Number of time periods
        unit_inventory_cost: Cost per unit inventory per period
        unit_production_cost: Cost per unit production
        unit_setup_cost: Cost per setup
        unit_opportunity_cost: Cost per unit opportunity loss
        unit_revenue: Revenue per unit sold
        setup_time: Time required for setup
        max_load_time: Maximum production time per period (minutes)
        max_setups: Maximum number of setups per period

    Returns:
        ProductionScheduler instance
    """
    # Load CSV data
    df = pd.read_csv(csv_file)

    m = len(df)  # number of products
    n = n_periods  # number of periods

    # Extract data from CSV
    part_numbers = df['part_number'].tolist()
    shipments = df['shipment'].tolist()  # Total demand across all periods
    capacities = df['capacity'].tolist()  # Production capacity per period
    cycle_times = df['cycle_time'].tolist()  # Production time per unit (seconds)
    initial_inventories = df['initial_inventory'].tolist()

    # Convert cycle times from seconds to minutes
    a_i = [ct / 60.0 for ct in cycle_times]  # production time per unit in minutes

    # Cost parameters (can be made product-specific if needed)
    v_i = [unit_inventory_cost] * m  # inventory cost
    c_i = [unit_production_cost] * m  # production cost
    s_i = [unit_setup_cost] * m  # setup cost
    h_i = [unit_opportunity_cost] * m  # opportunity loss cost

    # Setup time (can be made product-specific if needed)
    b_i = [setup_time] * m  # setup time in minutes

    # Create demand matrix - distribute total shipment across periods
    # Simple approach: equal distribution with some variation
    d_it = np.zeros((m, n))
    for i in range(m):
        base_demand = shipments[i] / n
        # Add some variation across periods (can be customized)
        for t in range(n):
            variation = 0.8 + 0.4 * np.random.random()  # 0.8 to 1.2 multiplier
            d_it[i][t] = max(0, base_demand * variation)

    # Revenue matrix (constant across periods, can be customized)
    r_it = np.full((m, n), unit_revenue)

    # Create and return scheduler
    return ProductionScheduler(
        m=m, n=n,
        v_i=v_i, c_i=c_i, s_i=s_i, h_i=h_i,
        d_it=d_it, r_it=r_it,
        a_i=a_i, b_i=b_i,
        T=max_load_time, U=max_setups,
        initial_inventory=initial_inventories
    )


def create_sample_problem():
    """Create a sample problem for testing"""
    m, n = 3, 4  # 3 products, 4 periods

    # Cost parameters
    v_i = [1.0, 1.5, 2.0]  # inventory cost
    c_i = [10.0, 15.0, 20.0]  # production cost
    s_i = [50.0, 60.0, 70.0]  # setup cost
    h_i = [100.0, 120.0, 150.0]  # opportunity loss cost

    # Demand and revenue matrices
    d_it = np.array([[10, 15, 20, 12],
                     [8, 12, 18, 10],
                     [5, 8, 15, 7]])

    r_it = np.array([[25, 25, 25, 25],
                     [30, 30, 30, 30],
                     [35, 35, 35, 35]])

    # Production parameters
    a_i = [2.0, 3.0, 4.0]  # production time per unit
    b_i = [30.0, 40.0, 50.0]  # setup time

    # Capacity
    T = 200.0  # maximum load time
    U = 2      # maximum setups

    return ProductionScheduler(m, n, v_i, c_i, s_i, h_i, d_it, r_it, a_i, b_i, T, U)


if __name__ == "__main__":
    # Example usage
    scheduler = create_sample_problem()
    status = scheduler.solve()
    print(f"Solution status: {status}")
    scheduler.print_solution()
