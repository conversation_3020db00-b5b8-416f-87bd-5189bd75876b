#!/usr/bin/env python3
"""
Test script to verify consistency between optimize_initial_inventory and adjust_initial_inventory functions.
"""

import pandas as pd
import csv
import copy
import random

# Set random seed for reproducibility
random.seed(42)

# Global variables (copied from both notebooks)
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []

# Cost parameters
在庫コスト単価 = 180
残業コスト単価 = 66.7
段替えコスト単価 = 130
出荷遅れコスト単価 = 500

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト

    with open(file_path, 'r', encoding='utf-8') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        
        rows = list(reader)
        for row in rows:
            if len(row) == 0:
                continue
            品番リスト.append(row[header.index("part_number")])
            出荷数リスト.append(int(row[header.index("shipment")]))
            収容数リスト.append(int(row[header.index("capacity")]))
            
            cycle_time_per_unit = float(row[header.index("cycle_time")]) / 60
            サイクルタイムリスト.append(cycle_time_per_unit)
            
            込め数リスト.append(int(row[header.index("cabity")]))
    
    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト

def simulate_production_schedule(initial_inventory, 期間=20):
    """生産スケジュールをシミュレートする関数（MIP用の簡易版）"""
    品番数 = len(initial_inventory)
    inventory = initial_inventory[:]
    inventory_history = [[] for _ in range(品番数)]
    
    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）
    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）
    max_daily_work_time = daily_regular_time + max_daily_overtime
    
    for t in range(期間):
        daily_production_time = 0
        daily_setup_count = 0
        
        # 各品番の需要を処理
        for i in range(品番数):
            demand = 出荷数リスト[i]
            inventory[i] -= demand
            
            # 在庫が不足する場合は生産
            if inventory[i] < 0:
                shortage = abs(inventory[i])
                # 生産量を決定（不足分を補う）
                production = shortage
                
                # 生産時間を計算
                if production > 0:
                    daily_setup_count += 1
                    setup_time = 30  # 段替え時間
                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
                    daily_production_time += production_time + setup_time
                
                inventory[i] += production
            
            # 在庫履歴に記録
            inventory_history[i].append(max(0, inventory[i]))
        
        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）
        if daily_production_time > max_daily_work_time:
            # 簡易的な調整：超過分を比例配分で削減
            reduction_factor = max_daily_work_time / daily_production_time
            for i in range(品番数):
                if inventory[i] > 0:
                    # 生産量を削減し、在庫を調整
                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))
                    reduced_production = current_production * reduction_factor
                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)
                    inventory[i] = max(0, inventory[i])
                    inventory_history[i][-1] = inventory[i]
    
    return inventory_history

def adjust_initial_inventory_mip_style(initial_inventory_list, h_cost, c_cost, num_simulations=50, max_iterations=5):
    """MIP版の初期在庫調整関数"""
    品番数 = len(initial_inventory_list)
    s = copy.deepcopy(initial_inventory_list)
    
    # h / (h+c) - optimize_initial_inventoryと同じ計算方法
    prob_target = h_cost / (h_cost + c_cost)
    
    print(f"\n=== MIP版: 初期在庫水準の調整アルゴリズム開始 ===")
    
    for iteration in range(max_iterations):
        print(f"--- 調整イテレーション {iteration + 1} ---")
        
        # 各在庫点について在庫量の分布を求める
        inventory_distributions = [[] for _ in range(品番数)]
        for _ in range(num_simulations):
            inventory_history = simulate_production_schedule(s)
            for i in range(品番数):
                inventory_distributions[i].extend(inventory_history[i])
        
        adjustments = [0] * 品番数
        
        # 各在庫点について在庫量の最適調整量r^*を求める
        for i in range(品番数):
            if not inventory_distributions[i]:
                continue
            
            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()
            cumulative_distribution = inventory_counts.cumsum()
            
            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック
            best_r = 0
            for r in cumulative_distribution.index:
                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)
                if prob_at_r_minus_1 <= prob_target:
                    best_r = r
                else:
                    break
            
            adjustments[i] = s[i] - best_r
            
        print(f"  今回の調整量: {adjustments}")
        
        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する
        new_s = [s[i] - adjustments[i] for i in range(品番数)]
        
        # 終了条件のチェック - optimize_initial_inventoryと同じ条件
        if all(abs(adj) < 1 for adj in adjustments):
            print("--- アルゴリズムが収束しました。---")
            return s
            
        s = new_s
        print(f"  更新後の初期在庫量: {s}")
        
    print("--- 最大反復回数に到達しました。---")
    return s

def test_consistency():
    """両関数の一貫性をテストする"""
    print("=== 一貫性テスト開始 ===")
    
    # データを読み込み
    read_csv('data.csv')
    品番数 = len(品番リスト)
    
    # テスト用の初期在庫を生成
    initial_inventory = [random.randint(shipment * 3, shipment * 5) for shipment in 出荷数リスト]
    
    print(f"品番数: {品番数}")
    print(f"初期在庫: {initial_inventory}")
    
    # 同じパラメータで両方の関数をテスト
    h_cost = 在庫コスト単価  # 180
    c_cost = 出荷遅れコスト単価  # 500
    num_simulations = 10  # 小さい値でテスト
    max_iterations = 3
    
    print(f"\nテストパラメータ:")
    print(f"  h_cost (在庫コスト): {h_cost}")
    print(f"  c_cost (出荷遅れコスト): {c_cost}")
    print(f"  num_simulations: {num_simulations}")
    print(f"  max_iterations: {max_iterations}")
    
    # MIP版の関数をテスト
    result_mip = adjust_initial_inventory_mip_style(
        initial_inventory, h_cost, c_cost, num_simulations, max_iterations
    )
    
    print(f"\n=== テスト結果 ===")
    print(f"MIP版結果: {result_mip}")
    
    # 結果の妥当性チェック
    print(f"\n=== 結果分析 ===")
    for i in range(品番数):
        change = result_mip[i] - initial_inventory[i]
        print(f"製品 {品番リスト[i]}: {initial_inventory[i]} → {result_mip[i]} (変化: {change:+.1f})")
    
    print("\n=== テスト完了 ===")
    return result_mip

if __name__ == "__main__":
    test_consistency()
