{"cells": [{"cell_type": "code", "execution_count": 22, "id": "6e757c78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [2141, 907, 2174, 166, 3208, 30]\n", "  更新後の初期在庫量: [15, 16, 16, 15, 16, 8]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [3, 3, 3, 2, 3, 0]\n", "  更新後の初期在庫量: [12, 13, 13, 13, 13, 8]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [-1, 1, 0, 1, 0, 0]\n", "  更新後の初期在庫量: [13, 12, 13, 12, 13, 8]\n", "--- 調整イテレーション 4 ---\n", "  今回の調整量: [-1, -2, 1, 0, 1, 1]\n", "  更新後の初期在庫量: [14, 14, 12, 12, 12, 7]\n", "--- 調整イテレーション 5 ---\n", "  今回の調整量: [1, 1, -1, 0, -3, -1]\n", "  更新後の初期在庫量: [13, 13, 13, 12, 15, 8]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタートローカルサーチ 生産スケジューリング最適化システム ===\n", "--- Start 1/30 ---\n", "  New best solution found with total cost: 500010.00\n", "--- Start 2/30 ---\n", "  New best solution found with total cost: 497480.00\n", "--- Start 3/30 ---\n", "  New best solution found with total cost: 392130.00\n", "--- Start 4/30 ---\n", "--- Start 5/30 ---\n", "--- Start 6/30 ---\n", "--- Start 7/30 ---\n", "--- Start 8/30 ---\n", "--- Start 9/30 ---\n", "--- Start 10/30 ---\n", "--- Start 11/30 ---\n", "--- Start 12/30 ---\n", "--- Start 13/30 ---\n", "--- Start 14/30 ---\n", "--- Start 15/30 ---\n", "--- Start 16/30 ---\n", "--- Start 17/30 ---\n", "--- Start 18/30 ---\n", "--- Start 19/30 ---\n", "--- Start 20/30 ---\n", "--- Start 21/30 ---\n", "--- Start 22/30 ---\n", "--- Start 23/30 ---\n", "--- Start 24/30 ---\n", "--- Start 25/30 ---\n", "--- Start 26/30 ---\n", "--- Start 27/30 ---\n", "--- Start 28/30 ---\n", "--- Start 29/30 ---\n", "--- Start 30/30 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 392130.00\n", "\n", "=== 結果のプロット ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["時間制約違反: 0 期間\n"]}], "source": ["import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "品番数 = 0\n", "期間 = 20  # 稼働20日\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7  # 4,000円/時間\n", "段替えコスト単価 = 130\n", "出荷遅れコスト単価 = 500  # 1個あたりの出荷遅れコスト\n", "品切れ率の許容値 = 0.05\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        # CSVからデータを読み込む\n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            品番リスト.append(row[header.index(\"part_number\")])\n", "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n", "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n", "            \n", "            # サイクルタイムを分単位に変換\n", "            cycle_time_per_unit = float(row[header.index(\"cycle_time\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n", "\n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(shipment * 3, shipment * 5)\n", "            初期在庫量リスト.append(random_inventory)\n", "\n", "        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(solution, current_initial_inventory):\n", "    \"\"\"総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数\"\"\"\n", "    global 品番数, 期間\n", "    \n", "    # 最小化コスト\n", "    total_inventory_cost = 0\n", "    total_overtime_cost = 0\n", "    total_setup_cost = 0\n", "    total_shipment_delay_cost = 0\n", "    \n", "    inventory = current_initial_inventory[:]\n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    \n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            production = solution[t][i]\n", "            \n", "            # 生産がある場合に段替えをする\n", "            if production > 0:\n", "                daily_setup_count += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "            \n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory_history[i].append(inventory[i])\n", "            \n", "            # 出荷遅れコスト\n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount\n", "            \n", "            # 在庫コスト\n", "            if inventory[i] > 0:\n", "                total_inventory_cost += 在庫コスト単価 * inventory[i]\n", "        \n", "        # 段替えコスト\n", "        total_setup_cost += 段替えコスト単価 * daily_setup_count\n", "        \n", "        # 残業コスト\n", "        if daily_time > daily_regular_time:\n", "            overtime = daily_time - daily_regular_time\n", "            total_overtime_cost += 残業コスト単価 * overtime\n", "        \n", "        # 最大稼働時間超過ペナルティ（違反する場合はコストに加算）\n", "        if daily_time > daily_regular_time + max_daily_overtime:\n", "            work_time_penalty = (daily_time - (daily_regular_time + max_daily_overtime)) * (残業コスト単価 * 1000000)\n", "            total_overtime_cost += work_time_penalty\n", "            \n", "    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost\n", "    \n", "    return total_cost, inventory_history\n", "\n", "def generate_initial_solution(current_initial_inventory):\n", "    \"\"\"初期解を生成する関数\"\"\"\n", "    solution = []\n", "    temp_inventory = current_initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_productions = [0] * 品番数\n", "        daily_time = 0\n", "        \n", "        # 在庫がない品番を優先的に生産する\n", "        priorities = []\n", "        for i in range(品番数):\n", "            shortage_estimate = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            priorities.append((shortage_estimate, i))\n", "        \n", "        priorities.sort(key=lambda x: x[0], reverse=True)\n", "        \n", "        for shortage_estimate, i in priorities:\n", "            if shortage_estimate > 0:\n", "                setup_time = 30\n", "                remaining_time = max_daily_work_time - daily_time\n", "                \n", "                if remaining_time > setup_time:\n", "                    cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]\n", "                    if cycle_time_per_unit > 0:\n", "                        max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)\n", "                        \n", "                        if max_producible_by_time > 0:\n", "                            target_production = shortage_estimate + random.randint(0, 50)\n", "                            production = min(target_production, max_producible_by_time)\n", "                            \n", "                            daily_productions[i] = production\n", "                            daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "        \n", "        solution.append(daily_productions)\n", "        \n", "        for i in range(品番数):\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            \n", "    return solution\n", "\n", "def get_neighbors(current_solution):\n", "    \"\"\"近傍解を生成する関数\"\"\"\n", "    neighbors = []\n", "    \n", "    # 2つの生産量を入れ替える\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "        i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "        \n", "        if (t1, i1) != (t2, i2):\n", "            neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "            neighbors.append(neighbor)\n", "            \n", "    # 特定の生産量を増減させる\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t = random.randint(0, 期間 - 1)\n", "        i = random.randint(0, 品番数 - 1)\n", "        \n", "        change = random.randint(-50, 50)\n", "        neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "        neighbors.append(neighbor)\n", "        \n", "    return neighbors\n", "\n", "def local_search(initial_solution, current_initial_inventory):\n", "    \"\"\"ローカルサーチを実行する関数\"\"\"\n", "    current_solution = initial_solution\n", "    current_cost, _ = evaluate(current_solution, current_initial_inventory)\n", "    \n", "    while True:\n", "        neighbors = get_neighbors(current_solution)\n", "        best_neighbor = None\n", "        best_neighbor_cost = float('inf')\n", "        \n", "        for neighbor in neighbors:\n", "            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)\n", "            if neighbor_cost < best_neighbor_cost:\n", "                best_neighbor = neighbor\n", "                best_neighbor_cost = neighbor_cost\n", "        \n", "        if best_neighbor_cost < current_cost:\n", "            current_solution = best_neighbor\n", "            current_cost = best_neighbor_cost\n", "        else:\n", "            break\n", "            \n", "    return current_solution, current_cost\n", "\n", "def multi_start_local_search(num_starts, current_initial_inventory):\n", "    \"\"\"多スタートローカルサーチを実行する関数\"\"\"\n", "    best_solution_overall = None\n", "    best_cost_overall = float('inf')\n", "    \n", "    for i in range(num_starts):\n", "        print(f\"--- Start {i+1}/{num_starts} ---\")\n", "        \n", "        initial_solution = generate_initial_solution(current_initial_inventory)\n", "        \n", "        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory)\n", "        \n", "        if local_optimal_cost < best_cost_overall:\n", "            best_cost_overall = local_optimal_cost\n", "            best_solution_overall = local_optimal_solution\n", "            print(f\"  New best solution found with total cost: {best_cost_overall:.2f}\")\n", "            \n", "    return best_solution_overall, best_cost_overall\n", "\n", "def optimize_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):\n", "    \"\"\"初期在庫量を最適化する関数\"\"\"\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    print(\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration+1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(50):\n", "            test_solution = generate_initial_solution(s)\n", "            _, inventory_history = evaluate(test_solution, s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c)\n", "            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "            \n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "\n", "def plot_results(best_individual, initial_inventory):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            production = best_individual[t][i]\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間（制限ラインを追加）\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "\n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "\n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period\n", "\n", "\n", "def main():\n", "    \"\"\"メイン実行関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "\n", "    result = read_csv('data.csv')\n", "    if result[0] is None:\n", "        print(\"CSVファイルの読み込みに失敗しました\")\n", "        return\n", "\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    \n", "    # 初期在庫量を更新処理\n", "    optimized_initial_inventory = optimize_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, max_iterations=5)\n", "    初期在庫量リスト = optimized_initial_inventory\n", "\n", "    print(\"=== 多スタートローカルサーチ 生産スケジューリング最適化システム ===\")\n", "    \n", "    num_starts = 30\n", "    best_solution, best_cost = multi_start_local_search(num_starts, 初期在庫量リスト)\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "\n", "        plot_results(best_solution, 初期在庫量リスト)\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "cfc95a61", "metadata": {}, "source": ["初期在庫の更新をMIPの方と合わせる  "]}, {"cell_type": "markdown", "id": "ce4e8fa9", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}