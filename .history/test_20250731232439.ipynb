{"cells": [{"cell_type": "code", "execution_count": null, "id": "84190747", "metadata": {}, "outputs": [{"ename": "PulpSolverError", "evalue": "PuLP: cannot execute glpsol", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPulpSolverError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 141\u001b[0m\n\u001b[1;32m    136\u001b[0m     prob \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m makespan \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m completion_time[job[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mid\u001b[39m\u001b[38;5;124m'\u001b[39m]], \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMakespanConstraint_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mjob[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mid\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    138\u001b[0m \u001b[38;5;66;03m# --- 6. 求解 ---\u001b[39;00m\n\u001b[1;32m    139\u001b[0m \n\u001b[1;32m    140\u001b[0m \u001b[38;5;66;03m# GLPKソルバーを指定して問題を解く [cite: 9]\u001b[39;00m\n\u001b[0;32m--> 141\u001b[0m prob\u001b[38;5;241m.\u001b[39msolve(GLPK())\n\u001b[1;32m    143\u001b[0m \u001b[38;5;66;03m# --- 7. 結果の表示 ---\u001b[39;00m\n\u001b[1;32m    145\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStatus:\u001b[39m\u001b[38;5;124m\"\u001b[39m, LpStatus[prob\u001b[38;5;241m.\u001b[39mstatus])\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/pulp/pulp.py:2007\u001b[0m, in \u001b[0;36mLpProblem.solve\u001b[0;34m(self, solver, **kwargs)\u001b[0m\n\u001b[1;32m   2005\u001b[0m \u001b[38;5;66;03m# time it\u001b[39;00m\n\u001b[1;32m   2006\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstartClock()\n\u001b[0;32m-> 2007\u001b[0m status \u001b[38;5;241m=\u001b[39m solver\u001b[38;5;241m.\u001b[39mactualSolve(\u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m   2008\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstopClock()\n\u001b[1;32m   2009\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrestoreObjective(wasNone, dummyVar)\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/pulp/apis/glpk_api.py:77\u001b[0m, in \u001b[0;36mGLPK_CMD.actualSolve\u001b[0;34m(self, lp)\u001b[0m\n\u001b[1;32m     75\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Solve a well formulated lp problem\"\"\"\u001b[39;00m\n\u001b[1;32m     76\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexecutable(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpath):\n\u001b[0;32m---> 77\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m PulpSolverError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPuLP: cannot execute \u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpath)\n\u001b[1;32m     78\u001b[0m tmpLp, tmpSol \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcreate_tmp_files(lp\u001b[38;5;241m.\u001b[39mname, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlp\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msol\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     79\u001b[0m lp\u001b[38;5;241m.\u001b[39mwriteLP(tmpLp, writeSOS\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n", "\u001b[0;31mPulpSolverError\u001b[0m: PuLP: cannot execute glpsol"]}], "source": ["from pulp import *\n", "\n", "# --- 1. データの定義 (課題のデータに基づいて定義) ---\n", "\n", "# セット\n", "PRODUCTS = ['製品A', '製品B', '製品C']  # 製品 [cite: 126]\n", "INTERMEDIATES = ['中間製品1', '中間製品2', '中間製品3']  # 中間製品 [cite: 126]\n", "MACHINES_INTERMEDIATE = ['M1', 'M2', 'M3', 'M4', 'M5', 'M6']  # 中間製品加工機 [cite: 109, 126]\n", "MACHINES_ASSEMBLY = ['M7']  # 組み立て機 [cite: 109, 126]\n", "ALL_MACHINES = MACHINES_INTERMEDIATE + MACHINES_ASSEMBLY\n", "ALL_ITEMS = PRODUCTS + INTERMEDIATES\n", "\n", "# パラメーター\n", "# ロットサイズ (個数/ロット) [cite: 126]\n", "lot_size = {item: 10 for item in ALL_ITEMS}\n", "\n", "# 加工時間 (分) [cite: 126]\n", "# 中間製品の加工時間\n", "proc_time_intermediate = {\n", "    ('中間製品1', 'M1'): 4, ('中間製品1', 'M2'): 5, ('中間製品1', 'M3'): 4,\n", "    ('中間製品1', 'M4'): 5, ('中間製品1', 'M5'): 6, ('中間製品1', 'M6'): 3,\n", "    ('中間製品2', 'M1'): 5, ('中間製品2', 'M2'): 5, ('中間製品2', 'M3'): 4,\n", "    ('中間製品2', 'M4'): 3, ('中間製品2', 'M5'): 3, ('中間製品2', 'M6'): 3,\n", "    ('中間製品3', 'M1'): 4, ('中間製品3', 'M2'): 5, ('中間製品3', 'M3'): 6,\n", "    ('中間製品3', 'M4'): 4, ('中間製品3', 'M5'): 6, ('中間製品3', 'M6'): 6\n", "}\n", "# 製品の組み立て時間 (M7) [cite: 126]\n", "proc_time_assembly = {\n", "    ('製品A', 'M7'): 12,\n", "    ('製品B', 'M7'): 14,\n", "    ('製品C', 'M7'): 16\n", "}\n", "\n", "# 全ての作業時間 [cite: 126]\n", "processing_time = {\n", "    (item, machine): proc_time_intermediate.get((item, machine), 0)\n", "    for item in INTERMEDIATES for machine in MACHINES_INTERMEDIATE\n", "}\n", "processing_time.update({\n", "    (item, machine): proc_time_assembly.get((item, machine), 0)\n", "    for item in PRODUCTS for machine in MACHINES_ASSEMBLY\n", "})\n", "\n", "# 計画条件（生産量とロット数） [cite: 133]\n", "plan_conditions = [\n", "    {'product': '製品A', 'start_time': 0, 'quantity': 50},\n", "    {'product': '製品B', 'start_time': 0, 'quantity': 60},\n", "    {'product': '製品C', 'start_time': 0, 'quantity': 40},\n", "    {'product': '製品A', 'start_time': 120, 'quantity': 40},\n", "    {'product': '製品B', 'start_time': 120, 'quantity': 50},\n", "    {'product': '製品C', 'start_time': 120, 'quantity': 60},\n", "    {'product': '製品A', 'start_time': 240, 'quantity': 50},\n", "    {'product': '製品B', 'start_time': 240, 'quantity': 40},\n", "    {'product': '製品C', 'start_time': 240, 'quantity': 40},\n", "]\n", "\n", "# ロットごとのジョブを定義 [cite: 128]\n", "jobs = []\n", "for cond in plan_conditions:\n", "    num_lots = cond['quantity'] // lot_size[cond['product']]\n", "    for i in range(num_lots):\n", "        job_id = f\"{cond['product']}_lot{i+1}_{cond['start_time']}\"\n", "        jobs.append({'id': job_id, 'product': cond['product'], 'start_time': cond['start_time']})\n", "\n", "# 切り替え時間 [cite: 135]\n", "setup_time = {\n", "    ('中間製品1', '中間製品2'): 8, ('中間製品1', '中間製品3'): 7,\n", "    ('中間製品2', '中間製品1'): 8, ('中間製品2', '中間製品3'): 9,\n", "    ('中間製品3', '中間製品1'): 7, ('中間製品3', '中間製品2'): 9,\n", "}\n", "# 切り替え時間がない場合を0で定義\n", "for i in INTERMEDIATES:\n", "    for j in INTERMEDIATES:\n", "        if (i, j) not in setup_time and i != j:\n", "            setup_time[(i, j)] = 0\n", "\n", "# --- 2. モデルの構築 ---\n", "\n", "# 最適化問題のインスタンスを作成\n", "prob = LpProblem(\"Production_Scheduling\", LpMinimize)\n", "\n", "# --- 3. 変数の定義 ---\n", "\n", "# 各ジョブの開始時刻と完了時刻 [cite: 130]\n", "start_time = LpVariable.dicts(\"S\", (job['id'] for job in jobs), 0, None, LpContinuous)\n", "completion_time = LpVariable.dicts(\"C\", (job['id'] for job in jobs), 0, None, LpContinuous)\n", "\n", "# 機械ごとの作業順序を示すバイナリ変数 [cite: 32]\n", "order = LpVariable.dicts(\"X\", [(job1['id'], job2['id'], m) for job1 in jobs for job2 in jobs if job1['id'] != job2['id'] for m in ALL_MACHINES], 0, 1, LpBinary)\n", "\n", "# メイクスパン\n", "makespan = LpVariable(\"Makespan\", 0, None, LpContinuous)\n", "\n", "# --- 4. 目的関数の定義 ---\n", "\n", "# メイクスパンの最小化 [cite: 32]\n", "prob += makespan, \"Minimizing Makespan\"\n", "\n", "# --- 5. 制約条件の追加 ---\n", "\n", "# 作業開始・完了時刻の関係 [cite: 130]\n", "for job in jobs:\n", "    job_id = job['id']\n", "    product = job['product']\n", "    # 中間製品のロットを考慮\n", "    intermediate_proc_time = [\n", "        processing_time.get((inter, m), 0)\n", "        for inter in INTERMEDIATES\n", "        for m in MACHINES_INTERMEDIATE\n", "    ]\n", "    # 最終製品の組み立てを考慮\n", "    assembly_proc_time = processing_time.get((product, 'M7'), 0)\n", "    \n", "    # ここでは単純化のため、各ジョブの総作業時間を仮定\n", "    total_job_time = sum(intermediate_proc_time) / len(intermediate_proc_time) + assembly_proc_time\n", "    prob += completion_time[job_id] >= start_time[job_id] + total_job_time, f\"CompletionTime_{job_id}\"\n", "\n", "# 機械能力制約 (Big-M法)\n", "BigM = 10000\n", "for m in ALL_MACHINES:\n", "    for i in range(len(jobs)):\n", "        for k in range(i + 1, len(jobs)):\n", "            job1_id = jobs[i]['id']\n", "            job2_id = jobs[k]['id']\n", "            \n", "            # 各ジョブの製品名を取得して切り替え時間を適用\n", "            item1 = jobs[i]['product']\n", "            item2 = jobs[k]['product']\n", "            st = setup_time.get((item1, item2), 0)\n", "            \n", "            prob += start_time[job1_id] >= completion_time[job2_id] + st - BigM * order[job1_id, job2_id, m], f\"MachineOrder1_{job1_id}_{job2_id}_{m}\"\n", "            prob += start_time[job2_id] >= completion_time[job1_id] + st - BigM * (1 - order[job1_id, job2_id, m]), f\"MachineOrder2_{job1_id}_{job2_id}_{m}\"\n", "\n", "# メイクスパンの定義 [cite: 32]\n", "for job in jobs:\n", "    prob += makespan >= completion_time[job['id']], f\"MakespanConstraint_{job['id']}\"\n", "    \n", "# --- 6. 求解 ---\n", "# CBCソルバーを明示的に使用（GLPKの問題を回避）\n", "try:\n", "    # CBCソルバーを試す\n", "    status = prob.solve(PULP_CBC_CMD(msg=1))\n", "    print(f\"CBC solver used. Status: {LpStatus[status]}\")\n", "except:\n", "    try:\n", "        # COINソルバーを試す\n", "        status = prob.solve(COIN_CMD(msg=1))\n", "        print(f\"COIN solver used. Status: {LpStatus[status]}\")\n", "    except:\n", "        # デフォルトソルバーを試す\n", "        status = prob.solve()\n", "        print(f\"Default solver used. Status: {LpStatus[status]}\")\n", "\n", "# --- 7. 結果の表示 ---\n", "\n", "print(\"Status:\", LpStatus[prob.status])\n", "print(\"Optimal Makespan =\", value(prob.objective))\n", "\n", "# 実行可能なジョブのスケジュールを表示\n", "for job in jobs:\n", "    job_id = job['id']\n", "    print(f\"Job: {job_id}, Start: {value(start_time[job_id])}, End: {value(completion_time[job_id])}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}