"""
Enhanced GA Production Scheduler with Cost Minimization
======================================================
"""

import numpy as np
import pandas as pd
from dataclasses import dataclass
from typing import List, Dict
import random
from deap import base, creator, tools, algorithms
import warnings
warnings.filterwarnings('ignore')

np.random.seed(42)
random.seed(42)

@dataclass
class ProductionData:
    part_numbers: List[str]
    shipments: List[int]
    capacities: List[int]
    cycle_times: List[float]
    cavities: List[int]
    initial_inventories: List[int]

class CostParameters:
    def __init__(self, setup_cost=5000.0, overtime_cost=100.0, inventory_cost=10.0, shortage_penalty=50000.0):
        self.setup_cost_per_changeover = setup_cost
        self.overtime_cost_per_minute = overtime_cost
        self.inventory_holding_cost_per_unit_day = inventory_cost
        self.shortage_penalty_per_unit = shortage_penalty

class GAParameters:
    def __init__(self, population_size=150, generations=100, crossover_probability=0.7,
                 mutation_probability=0.2, tournament_size=3, elite_size=5):
        self.population_size = population_size
        self.generations = generations
        self.crossover_probability = crossover_probability
        self.mutation_probability = mutation_probability
        self.tournament_size = tournament_size
        self.elite_size = elite_size

class ProductionConstraints:
    def __init__(self, max_daily_time_minutes=1200, max_daily_setups=5, planning_periods=20):
        self.max_daily_time_minutes = max_daily_time_minutes
        self.max_daily_setups = max_daily_setups
        self.planning_periods = planning_periods

class EnhancedGAProductionScheduler:
    def __init__(self, production_data, cost_params, constraints, ga_params):
        self.production_data = production_data
        self.cost_params = cost_params
        self.constraints = constraints
        self.ga_params = ga_params
        self.num_products = len(production_data.part_numbers)
        self.num_periods = constraints.planning_periods
        self.best_solution = None
        self.best_cost = float('inf')
        self.cost_breakdown = None
        self._setup_deap()

    def _setup_deap(self):
        if hasattr(creator, 'FitnessMin'): del creator.FitnessMin
        if hasattr(creator, 'Individual'): del creator.Individual
        creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
        creator.create("Individual", list, fitness=creator.FitnessMin)
        self.toolbox = base.Toolbox()
        self.toolbox.register("individual", self._generate_individual)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        self.toolbox.register("evaluate", self._evaluate_individual)
        self.toolbox.register("mate", self._crossover)
        self.toolbox.register("mutate", self._mutate)
        self.toolbox.register("select", tools.selTournament, tournsize=self.ga_params.tournament_size)
    
    def _generate_individual(self):
        individual = []
        temp_inventory = self.production_data.initial_inventories[:]

        for period in range(self.num_periods):
            daily_time = 0
            daily_setups = 0
            period_production = [0] * self.num_products

            # Calculate priorities based on shortage risk
            priorities = [(max(0, self.production_data.shipments[i] - temp_inventory[i]) *
                          self.production_data.shipments[i], i) for i in range(self.num_products)]
            priorities.sort(reverse=True)

            for risk, product_idx in priorities:
                if daily_setups >= self.constraints.max_daily_setups:
                    break

                shortage = max(0, self.production_data.shipments[product_idx] - temp_inventory[product_idx])
                if shortage <= 0:
                    continue

                cycle_time = self.production_data.cycle_times[product_idx]
                cavity = self.production_data.cavities[product_idx]
                if cycle_time <= 0 or cavity <= 0:
                    continue

                time_per_unit = cycle_time / cavity
                setup_time = 30
                remaining_time = self.constraints.max_daily_time_minutes - daily_time - setup_time

                if remaining_time <= 0:
                    break

                max_producible = int(remaining_time / time_per_unit)
                if max_producible <= 0:
                    continue

                target_production = min(shortage * random.randint(1, 3), max_producible)
                production_quantity = random.randint(shortage, max(shortage, target_production))
                production_time = setup_time + production_quantity * time_per_unit

                if daily_time + production_time <= self.constraints.max_daily_time_minutes:
                    period_production[product_idx] = production_quantity
                    daily_time += production_time
                    daily_setups += 1
                    temp_inventory[product_idx] += production_quantity

            for i in range(self.num_products):
                temp_inventory[i] = max(0, temp_inventory[i] - self.production_data.shipments[i])

            individual.extend(period_production)

        return creator.Individual(individual)
    
    def _evaluate_individual(self, individual):
        setup_cost = overtime_cost = inventory_cost = shortage_penalty = 0.0
        inventory = self.production_data.initial_inventories[:]

        for period in range(self.num_periods):
            daily_time = daily_setups = 0

            for product_idx in range(self.num_products):
                idx = period * self.num_products + product_idx
                production = individual[idx]

                if production > 0:
                    daily_setups += 1
                    cycle_time = self.production_data.cycle_times[product_idx]
                    cavity = self.production_data.cavities[product_idx]
                    if cycle_time > 0 and cavity > 0:
                        daily_time += 30 + production * (cycle_time / cavity)

                inventory[product_idx] += production - self.production_data.shipments[product_idx]

                if inventory[product_idx] < 0:
                    shortage_penalty += abs(inventory[product_idx]) * self.cost_params.shortage_penalty_per_unit
                    inventory[product_idx] = 0

                inventory_cost += max(0, inventory[product_idx]) * self.cost_params.inventory_holding_cost_per_unit_day

            setup_cost += daily_setups * self.cost_params.setup_cost_per_changeover

            if daily_time > self.constraints.max_daily_time_minutes:
                overtime_cost += (daily_time - self.constraints.max_daily_time_minutes) * self.cost_params.overtime_cost_per_minute

        return (setup_cost + overtime_cost + inventory_cost + shortage_penalty,)
    
    def _crossover(self, ind1, ind2):
        for period in range(self.num_periods):
            if random.random() < 0.5:
                start_idx = period * self.num_products
                end_idx = start_idx + self.num_products
                ind1[start_idx:end_idx], ind2[start_idx:end_idx] = ind2[start_idx:end_idx], ind1[start_idx:end_idx]
        return ind1, ind2

    def _mutate(self, individual):
        for i in range(len(individual)):
            if random.random() < 0.1:
                current_value = individual[i]
                if current_value > 0:
                    individual[i] = max(0, current_value + random.randint(-50, 50))
                elif random.random() < 0.3:
                    product_idx = i % self.num_products
                    individual[i] = random.randint(0, self.production_data.shipments[product_idx] * 2)
        return (individual,)

    def optimize(self):
        print(f"Starting GA optimization with {self.ga_params.population_size} individuals for {self.ga_params.generations} generations...")

        population = self.toolbox.population(n=self.ga_params.population_size)
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("min", np.min)
        hof = tools.HallOfFame(self.ga_params.elite_size)

        population, logbook = algorithms.eaSimple(
            population, self.toolbox,
            cxpb=self.ga_params.crossover_probability,
            mutpb=self.ga_params.mutation_probability,
            ngen=self.ga_params.generations,
            stats=stats,
            halloffame=hof,
            verbose=True
        )

        self.best_solution = hof[0]
        self.best_cost = hof[0].fitness.values[0]
        self.cost_breakdown = self._calculate_cost_breakdown(self.best_solution)

        print(f"\nOptimization completed! Best total cost: ¥{self.best_cost:,.2f}")
        for cost_type, amount in self.cost_breakdown.items():
            print(f"  {cost_type}: ¥{amount:,.2f}")

        return self.best_solution, logbook

    def _calculate_cost_breakdown(self, individual):
        setup_cost = overtime_cost = inventory_cost = shortage_penalty = 0.0
        inventory = self.production_data.initial_inventories[:]

        for period in range(self.num_periods):
            daily_time = daily_setups = 0

            for product_idx in range(self.num_products):
                idx = period * self.num_products + product_idx
                production = individual[idx]

                if production > 0:
                    daily_setups += 1
                    cycle_time = self.production_data.cycle_times[product_idx]
                    cavity = self.production_data.cavities[product_idx]
                    if cycle_time > 0 and cavity > 0:
                        daily_time += 30 + production * (cycle_time / cavity)

                inventory[product_idx] += production - self.production_data.shipments[product_idx]

                if inventory[product_idx] < 0:
                    shortage_penalty += abs(inventory[product_idx]) * self.cost_params.shortage_penalty_per_unit
                    inventory[product_idx] = 0

                inventory_cost += max(0, inventory[product_idx]) * self.cost_params.inventory_holding_cost_per_unit_day

            setup_cost += daily_setups * self.cost_params.setup_cost_per_changeover

            if daily_time > self.constraints.max_daily_time_minutes:
                overtime_cost += (daily_time - self.constraints.max_daily_time_minutes) * self.cost_params.overtime_cost_per_minute

        return {
            'Setup Cost': setup_cost,
            'Overtime Cost': overtime_cost,
            'Inventory Cost': inventory_cost,
            'Shortage Penalty': shortage_penalty,
            'Total Cost': setup_cost + overtime_cost + inventory_cost + shortage_penalty
        }


def load_production_data(csv_file: str) -> ProductionData:
    """Load production data from CSV file"""
    try:
        df = pd.read_csv(csv_file)

        return ProductionData(
            part_numbers=df['part_number'].tolist(),
            shipments=df['shipment'].tolist(),
            capacities=df['capacity'].tolist(),
            cycle_times=df['cycle_time'].tolist(),
            cavities=df['cabity'].tolist(),  # Note: keeping original column name
            initial_inventories=df['initial_inventory'].tolist()
        )
    except Exception as e:
        print(f"Error loading data from {csv_file}: {e}")
        return None


def create_experiment_config(base_params, variations):
    configs = []
    for variation_name, param_changes in variations.items():
        config = GAParameters(
            population_size=param_changes.get('population_size', base_params.population_size),
            generations=param_changes.get('generations', base_params.generations),
            crossover_probability=param_changes.get('crossover_probability', base_params.crossover_probability),
            mutation_probability=param_changes.get('mutation_probability', base_params.mutation_probability),
            tournament_size=param_changes.get('tournament_size', base_params.tournament_size),
            elite_size=param_changes.get('elite_size', base_params.elite_size)
        )
        configs.append((variation_name, config))
    return configs


def run_single_experiment(production_data, cost_params=None, constraints=None, ga_params=None,
                         experiment_name="default", setup_cost=5000.0, overtime_cost=100.0,
                         inventory_cost=10.0, shortage_penalty=50000.0):
    print(f"Running experiment: {experiment_name}")

    if cost_params is None:
        cost_params = CostParameters(setup_cost, overtime_cost, inventory_cost, shortage_penalty)
    if constraints is None:
        constraints = ProductionConstraints()
    if ga_params is None:
        ga_params = GAParameters()

    scheduler = EnhancedGAProductionScheduler(production_data, cost_params, constraints, ga_params)
    scheduler.optimize()

    return {
        'experiment_name': experiment_name,
        'best_cost': scheduler.best_cost,
        'cost_breakdown': scheduler.cost_breakdown,
        'scheduler': scheduler
    }


def optimize_with_costs(data_file: str = 'data_ga.csv',
                       setup_cost: float = 5000.0,
                       overtime_cost: float = 100.0,
                       inventory_cost: float = 10.0,
                       shortage_penalty: float = 50000.0,
                       population_size: int = 150,
                       generations: int = 100,
                       crossover_prob: float = 0.7,
                       mutation_prob: float = 0.2,
                       max_daily_time: int = 1200,
                       max_daily_setups: int = 5,
                       planning_periods: int = 20,
                       experiment_name: str = "custom") -> Dict:
    """
    Convenient function to run optimization with all parameters as arguments

    Args:
        data_file: CSV file with production data
        setup_cost: Cost per production changeover (¥)
        overtime_cost: Cost per minute of overtime (¥)
        inventory_cost: Daily inventory holding cost per unit (¥)
        shortage_penalty: Penalty per unit of shortage (¥)
        population_size: GA population size
        generations: Number of GA generations
        crossover_prob: Crossover probability (0-1)
        mutation_prob: Mutation probability (0-1)
        max_daily_time: Maximum daily production time (minutes)
        max_daily_setups: Maximum daily setup operations
        planning_periods: Number of planning periods
        experiment_name: Name for this experiment

    Returns:
        Dictionary with optimization results
    """

    # Load data
    production_data = load_production_data(data_file)
    if production_data is None:
        raise ValueError(f"Failed to load data from {data_file}")

    # Create parameter objects
    cost_params = CostParameters(
        setup_cost=setup_cost,
        overtime_cost=overtime_cost,
        inventory_cost=inventory_cost,
        shortage_penalty=shortage_penalty
    )

    constraints = ProductionConstraints(
        max_daily_time_minutes=max_daily_time,
        max_daily_setups=max_daily_setups,
        planning_periods=planning_periods
    )

    ga_params = GAParameters(
        population_size=population_size,
        generations=generations,
        crossover_probability=crossover_prob,
        mutation_probability=mutation_prob
    )

    # Run optimization
    return run_single_experiment(
        production_data=production_data,
        cost_params=cost_params,
        constraints=constraints,
        ga_params=ga_params,
        experiment_name=experiment_name
    )


def compare_cost_scenarios(data_file: str = 'data_ga.csv',
                          base_setup_cost: float = 5000.0,
                          base_overtime_cost: float = 100.0,
                          base_inventory_cost: float = 10.0,
                          base_shortage_penalty: float = 50000.0,
                          cost_multipliers: List[Tuple[str, Dict]] = None) -> pd.DataFrame:
    """
    Compare different cost scenarios

    Args:
        data_file: CSV file with production data
        base_setup_cost: Base setup cost
        base_overtime_cost: Base overtime cost
        base_inventory_cost: Base inventory cost
        base_shortage_penalty: Base shortage penalty
        cost_multipliers: List of (name, multiplier_dict) tuples

    Returns:
        DataFrame comparing results
    """

    if cost_multipliers is None:
        cost_multipliers = [
            ("Baseline", {}),
            ("High Setup", {"setup": 2.0}),
            ("High Overtime", {"overtime": 2.0}),
            ("High Inventory", {"inventory": 2.0}),
            ("Low Setup", {"setup": 0.5}),
            ("Low Overtime", {"overtime": 0.5}),
            ("Low Inventory", {"inventory": 0.5})
        ]

    results = []

    for scenario_name, multipliers in cost_multipliers:
        setup = base_setup_cost * multipliers.get("setup", 1.0)
        overtime = base_overtime_cost * multipliers.get("overtime", 1.0)
        inventory = base_inventory_cost * multipliers.get("inventory", 1.0)
        shortage = base_shortage_penalty * multipliers.get("shortage", 1.0)

        print(f"Running scenario: {scenario_name}")
        print(f"  Setup: ¥{setup:,.0f}, Overtime: ¥{overtime:.0f}/min, Inventory: ¥{inventory:.0f}/unit/day")

        result = optimize_with_costs(
            data_file=data_file,
            setup_cost=setup,
            overtime_cost=overtime,
            inventory_cost=inventory,
            shortage_penalty=shortage,
            population_size=100,  # Smaller for quick comparison
            generations=50,
            experiment_name=scenario_name
        )

        results.append({
            'Scenario': scenario_name,
            'Total_Cost': result['best_cost'],
            'Setup_Cost': result['cost_breakdown']['Setup Cost'],
            'Overtime_Cost': result['cost_breakdown']['Overtime Cost'],
            'Inventory_Cost': result['cost_breakdown']['Inventory Cost'],
            'Shortage_Penalty': result['cost_breakdown']['Shortage Penalty'],
            'Setup_Rate': setup,
            'Overtime_Rate': overtime,
            'Inventory_Rate': inventory
        })

    return pd.DataFrame(results)


if __name__ == "__main__":
    # Example usage with cost arguments
    print("Enhanced GA Production Scheduler")
    print("===============================")

    # Example 1: Basic usage with default costs
    print("\nExample 1: Default costs")
    result1 = optimize_with_costs(
        experiment_name="default_costs"
    )
    print(f"Result: ¥{result1['best_cost']:,.2f}")

    # Example 2: High setup cost scenario
    print("\nExample 2: High setup cost")
    result2 = optimize_with_costs(
        setup_cost=10000.0,  # Double the setup cost
        experiment_name="high_setup"
    )
    print(f"Result: ¥{result2['best_cost']:,.2f}")

    # Example 3: High inventory cost scenario
    print("\nExample 3: High inventory cost")
    result3 = optimize_with_costs(
        inventory_cost=25.0,  # Higher inventory cost
        experiment_name="high_inventory"
    )
    print(f"Result: ¥{result3['best_cost']:,.2f}")

    # Example 4: Compare scenarios
    print("\nExample 4: Comparing cost scenarios")
    comparison_df = compare_cost_scenarios()
    print("\nComparison Results:")
    print(comparison_df[['Scenario', 'Total_Cost', 'Setup_Cost', 'Overtime_Cost', 'Inventory_Cost']].to_string(index=False))
