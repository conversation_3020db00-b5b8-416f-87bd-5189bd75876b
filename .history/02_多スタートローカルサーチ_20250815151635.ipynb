{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6e757c78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [2590, 1406, 1805, 214, 3966, 24]\n", "  更新後の初期在庫量: [17, 16, 15, 16, 16, 8]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [5, 2, 1, 4, 2, 0]\n", "  更新後の初期在庫量: [12, 14, 14, 12, 14, 8]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [-1, 0, 1, -1, 2, 1]\n", "  更新後の初期在庫量: [13, 14, 13, 13, 12, 7]\n", "--- 調整イテレーション 4 ---\n", "  今回の調整量: [0, 1, -1, 0, -1, -1]\n", "  更新後の初期在庫量: [13, 13, 14, 13, 13, 8]\n", "--- 調整イテレーション 5 ---\n", "  今回の調整量: [1, 0, 1, 2, 1, 1]\n", "  更新後の初期在庫量: [12, 13, 13, 11, 12, 7]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタートローカルサーチ 生産スケジューリング最適化システム ===\n", "--- Start 1/30 ---\n", "  New best solution found with total cost: 466960.00\n", "--- Start 2/30 ---\n", "  New best solution found with total cost: 465110.00\n", "--- Start 3/30 ---\n", "--- Start 4/30 ---\n", "--- Start 5/30 ---\n", "--- Start 6/30 ---\n", "--- Start 7/30 ---\n", "--- Start 8/30 ---\n", "--- Start 9/30 ---\n", "  New best solution found with total cost: 431960.00\n", "--- Start 10/30 ---\n", "--- Start 11/30 ---\n", "--- Start 12/30 ---\n", "--- Start 13/30 ---\n", "--- Start 14/30 ---\n", "--- Start 15/30 ---\n", "--- Start 16/30 ---\n", "--- Start 17/30 ---\n", "--- Start 18/30 ---\n", "--- Start 19/30 ---\n", "--- Start 20/30 ---\n", "--- Start 21/30 ---\n", "--- Start 22/30 ---\n", "--- Start 23/30 ---\n", "--- Start 24/30 ---\n", "--- Start 25/30 ---\n", "--- Start 26/30 ---\n", "--- Start 27/30 ---\n", "--- Start 28/30 ---\n", "--- Start 29/30 ---\n", "  New best solution found with total cost: 429400.00\n", "--- Start 30/30 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 429400.00\n", "\n", "=== 結果のプロット ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["時間制約違反: 0 期間\n"]}], "source": ["import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "品番数 = 0\n", "期間 = 20  # 稼働20日\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7  # 4,000円/時間\n", "段替えコスト単価 = 130\n", "出荷遅れコスト単価 = 500  # 1個あたりの出荷遅れコスト\n", "品切れ率の許容値 = 0.05\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        # CSVからデータを読み込む\n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            品番リスト.append(row[header.index(\"part_number\")])\n", "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n", "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n", "            \n", "            # サイクルタイムを分単位に変換\n", "            cycle_time_per_unit = float(row[header.index(\"cycle_time\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n", "\n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(shipment * 3, shipment * 5)\n", "            初期在庫量リスト.append(random_inventory)\n", "\n", "        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(solution, current_initial_inventory):\n", "    \"\"\"総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数\"\"\"\n", "    global 品番数, 期間\n", "    \n", "    # 最小化コスト\n", "    total_inventory_cost = 0\n", "    total_overtime_cost = 0\n", "    total_setup_cost = 0\n", "    total_shipment_delay_cost = 0\n", "    \n", "    inventory = current_initial_inventory[:]\n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    \n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            production = solution[t][i]\n", "            \n", "            # 生産がある場合に段替えをする\n", "            if production > 0:\n", "                daily_setup_count += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "            \n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory_history[i].append(inventory[i])\n", "            \n", "            # 出荷遅れコスト\n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount\n", "            \n", "            # 在庫コスト\n", "            if inventory[i] > 0:\n", "                total_inventory_cost += 在庫コスト単価 * inventory[i]\n", "        \n", "        # 段替えコスト\n", "        total_setup_cost += 段替えコスト単価 * daily_setup_count\n", "        \n", "        # 残業コスト\n", "        if daily_time > daily_regular_time:\n", "            overtime = daily_time - daily_regular_time\n", "            total_overtime_cost += 残業コスト単価 * overtime\n", "        \n", "        # 最大稼働時間超過ペナルティ（違反する場合はコストに加算）\n", "        if daily_time > daily_regular_time + max_daily_overtime:\n", "            work_time_penalty = (daily_time - (daily_regular_time + max_daily_overtime)) * (残業コスト単価 * 1000000)\n", "            total_overtime_cost += work_time_penalty\n", "            \n", "    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost\n", "    \n", "    return total_cost, inventory_history\n", "\n", "def generate_initial_solution(current_initial_inventory):\n", "    \"\"\"初期解を生成する関数\"\"\"\n", "    solution = []\n", "    temp_inventory = current_initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_productions = [0] * 品番数\n", "        daily_time = 0\n", "        \n", "        # 在庫がない品番を優先的に生産する\n", "        priorities = []\n", "        for i in range(品番数):\n", "            shortage_estimate = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            priorities.append((shortage_estimate, i))\n", "        \n", "        priorities.sort(key=lambda x: x[0], reverse=True)\n", "        \n", "        for shortage_estimate, i in priorities:\n", "            if shortage_estimate > 0:\n", "                setup_time = 30\n", "                remaining_time = max_daily_work_time - daily_time\n", "                \n", "                if remaining_time > setup_time:\n", "                    cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]\n", "                    if cycle_time_per_unit > 0:\n", "                        max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)\n", "                        \n", "                        if max_producible_by_time > 0:\n", "                            target_production = shortage_estimate + random.randint(0, 50)\n", "                            production = min(target_production, max_producible_by_time)\n", "                            \n", "                            daily_productions[i] = production\n", "                            daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "        \n", "        solution.append(daily_productions)\n", "        \n", "        for i in range(品番数):\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            \n", "    return solution\n", "\n", "def get_neighbors(current_solution):\n", "    \"\"\"近傍解を生成する関数\"\"\"\n", "    neighbors = []\n", "    \n", "    # 2つの生産量を入れ替える\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "        i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "        \n", "        if (t1, i1) != (t2, i2):\n", "            neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "            neighbors.append(neighbor)\n", "            \n", "    # 特定の生産量を増減させる\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t = random.randint(0, 期間 - 1)\n", "        i = random.randint(0, 品番数 - 1)\n", "        \n", "        change = random.randint(-50, 50)\n", "        neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "        neighbors.append(neighbor)\n", "        \n", "    return neighbors\n", "\n", "def local_search(initial_solution, current_initial_inventory):\n", "    \"\"\"ローカルサーチを実行する関数\"\"\"\n", "    current_solution = initial_solution\n", "    current_cost, _ = evaluate(current_solution, current_initial_inventory)\n", "    \n", "    while True:\n", "        neighbors = get_neighbors(current_solution)\n", "        best_neighbor = None\n", "        best_neighbor_cost = float('inf')\n", "        \n", "        for neighbor in neighbors:\n", "            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)\n", "            if neighbor_cost < best_neighbor_cost:\n", "                best_neighbor = neighbor\n", "                best_neighbor_cost = neighbor_cost\n", "        \n", "        if best_neighbor_cost < current_cost:\n", "            current_solution = best_neighbor\n", "            current_cost = best_neighbor_cost\n", "        else:\n", "            break\n", "            \n", "    return current_solution, current_cost\n", "\n", "def multi_start_local_search(num_starts, current_initial_inventory):\n", "    \"\"\"多スタートローカルサーチを実行する関数\"\"\"\n", "    best_solution_overall = None\n", "    best_cost_overall = float('inf')\n", "    \n", "    for i in range(num_starts):\n", "        print(f\"--- Start {i+1}/{num_starts} ---\")\n", "        \n", "        initial_solution = generate_initial_solution(current_initial_inventory)\n", "        \n", "        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory)\n", "        \n", "        if local_optimal_cost < best_cost_overall:\n", "            best_cost_overall = local_optimal_cost\n", "            best_solution_overall = local_optimal_solution\n", "            print(f\"  New best solution found with total cost: {best_cost_overall:.2f}\")\n", "            \n", "    return best_solution_overall, best_cost_overall\n", "\n", "def simulate_production_schedule_simple(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def optimize_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):\n", "    \"\"\"初期在庫量を最適化する関数\"\"\"\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    print(\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration+1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule_simple(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c)\n", "            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "            \n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "\n", "def plot_results(best_individual, initial_inventory):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            production = best_individual[t][i]\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間（制限ラインを追加）\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "\n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "\n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period\n", "\n", "\n", "def main():\n", "    \"\"\"メイン実行関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "\n", "    result = read_csv('data.csv')\n", "    if result[0] is None:\n", "        print(\"CSVファイルの読み込みに失敗しました\")\n", "        return\n", "\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    \n", "    # 初期在庫量を更新処理\n", "    optimized_initial_inventory = optimize_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5)\n", "    初期在庫量リスト = optimized_initial_inventory\n", "\n", "    print(\"=== 多スタートローカルサーチ 生産スケジューリング最適化システム ===\")\n", "    \n", "    num_starts = 30\n", "    best_solution, best_cost = multi_start_local_search(num_starts, 初期在庫量リスト)\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "\n", "        plot_results(best_solution, 初期在庫量リスト)\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "cfc95a61", "metadata": {}, "source": ["初期在庫の更新をMIPの方と合わせる  "]}, {"cell_type": "markdown", "id": "ce4e8fa9", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}