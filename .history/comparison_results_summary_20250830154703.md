# 生産スケジューリング最適化手法比較結果

## 概要
このドキュメントは、混合整数計画法（MIP）と多スタートローカルサーチの2つの最適化手法を比較した結果をまとめています。

## 使用したツール
- **ファイル名**: `comparison_optimizer.py`
- **データファイル**: `data.csv`
- **実行環境**: Python with PuLP (Gurobi solver)

## 比較結果

### 数値比較
| 手法 | ステータス | 目的関数値 | 計算時間 (秒) |
|------|------------|------------|---------------|
| 混合整数計画法 (MIP) | Optimal | 790,783.33 | 0.0336 |
| 多スタートローカルサーチ | Local Optimum | 974,216.67 | 0.0481 |

### 詳細分析
- **目的関数値の差**: 183,433.33
- **MIPの優位性**: 18.83% 良い解を発見
- **解の品質比**: MIPの解はローカルサーチの解より 1.23 倍良い
- **計算速度**: MIPの方が 1.43 倍高速

## 主要な発見

### 1. 解の品質
- **MIP**: 数学的に保証された最適解を発見
- **ローカルサーチ**: 局所最適解に留まり、約19%劣る結果

### 2. 計算効率
- **MIP**: 0.0336秒で最適解を発見
- **ローカルサーチ**: 0.0481秒で30回の試行を実行

### 3. 安定性
- **MIP**: 毎回同じ最適解を保証
- **ローカルサーチ**: 初期解に依存し、結果にばらつきがある

## 生産スケジュール例（MIP結果）
```
製品A: Day1=1461, Day2-20=600 (毎日)
製品B: Day1=0, Day2=722, Day3-20=300 (毎日)
製品C: Day1=0, Day2=1081, Day3-20=500 (毎日)
製品D: Day1=0, Day2=129, Day3-20=50 (毎日)
製品E: Day1=2748, Day2-20=1000 (毎日)
製品F: Day1-2=0, Day3=31, Day4-20=10 (毎日)
```

## 結論

この比較により、以下のことが明らかになりました：

1. **MIPの圧倒的優位性**: 解の品質と計算速度の両面でMIPが優秀
2. **実用性**: 小規模〜中規模の生産スケジューリング問題では、MIPが最適
3. **スケーラビリティ**: より大規模な問題では、ローカルサーチの価値が高まる可能性

## 推奨事項

- **現在の問題規模**: MIPを第一選択として使用
- **将来の拡張**: 問題規模が大幅に増加した場合のみローカルサーチを検討
- **ハイブリッド手法**: MIPで得られた解をローカルサーチの初期解として使用する手法も検討可能

## ツールの使用方法

```python
# 基本的な実行
python comparison_optimizer.py

# カスタマイズされた実行
from comparison_optimizer import run_comparison, print_comparison_results

results, adjusted_inventory = run_comparison(
    csv_file='your_data.csv',
    num_starts=50,  # ローカルサーチの試行回数
    verbose=True
)

print_comparison_results(results)
```

## 技術的詳細

### MIP定式化
- 目的関数: 在庫コスト + 残業コスト + 段替えコスト + 出荷遅れコスト
- 制約: 在庫バランス、生産能力、時間制約

### ローカルサーチ手法
- 初期解生成: 出荷遅れを優先した貪欲法
- 近傍操作: 生産量の交換と増減
- 多スタート: 30回の独立した試行

### パラメータ設定
- 在庫コスト単価: 70
- 残業コスト単価: 500
- 段替えコスト単価: 500
- 出荷遅れコスト単価: 500
- 定時: 960分/日 (8時間×2シフト)
- 最大残業: 240分/日 (2時間×2シフト)
