{"cells": [{"cell_type": "code", "execution_count": 8, "id": "6e757c78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [600, 300, 500, 50, 1000, 10]\n", "  更新後の初期在庫量: [2171, 802, 1274, 162, 2109, 37]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [600, 300, 500, 50, 1000, 10]\n", "  更新後の初期在庫量: [1571, 502, 774, 112, 1109, 27]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [600, 300, 500, 50, 1000, 10]\n", "  更新後の初期在庫量: [971, 202, 274, 62, 109, 17]\n", "--- 調整イテレーション 4 ---\n", "  今回の調整量: [600, 202, 274, 50, 109, 10]\n", "  更新後の初期在庫量: [371, 0, 0, 12, 0, 7]\n", "--- 調整イテレーション 5 ---\n", "  今回の調整量: [371, 0, 0, 12, 0, 7]\n", "  更新後の初期在庫量: [0, 0, 0, 0, 0, 0]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタートローカルサーチ 生産スケジューリング最適化システム ===\n", "--- Start 1/30 ---\n", "  New best solution found with total cost: 534740.00\n", "--- Start 2/30 ---\n", "--- Start 3/30 ---\n", "  New best solution found with total cost: 442830.00\n", "--- Start 4/30 ---\n", "--- Start 5/30 ---\n", "--- Start 6/30 ---\n", "--- Start 7/30 ---\n", "  New best solution found with total cost: 420760.00\n", "--- Start 8/30 ---\n", "--- Start 9/30 ---\n", "--- Start 10/30 ---\n", "--- Start 11/30 ---\n", "--- Start 12/30 ---\n", "--- Start 13/30 ---\n", "  New best solution found with total cost: 387920.00\n", "--- Start 14/30 ---\n", "--- Start 15/30 ---\n", "--- Start 16/30 ---\n", "--- Start 17/30 ---\n", "--- Start 18/30 ---\n", "--- Start 19/30 ---\n", "--- Start 20/30 ---\n", "--- Start 21/30 ---\n", "--- Start 22/30 ---\n", "--- Start 23/30 ---\n", "--- Start 24/30 ---\n", "--- Start 25/30 ---\n", "--- Start 26/30 ---\n", "--- Start 27/30 ---\n", "--- Start 28/30 ---\n", "--- Start 29/30 ---\n", "--- Start 30/30 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 387920.00\n", "\n", "=== 結果のプロット ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["時間制約違反: 0 期間\n"]}], "source": ["import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "品番数 = 0\n", "期間 = 20  # 稼働20日\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7  # 4,000円/時間\n", "段替えコスト単価 = 130\n", "出荷遅れコスト単価 = 500  # 1個あたりの出荷遅れコスト\n", "品切れ率の許容値 = 0.05\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        # CSVからデータを読み込む\n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            品番リスト.append(row[header.index(\"part_number\")])\n", "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n", "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n", "            \n", "            # サイクルタイムを分単位に変換\n", "            cycle_time_per_unit = float(row[header.index(\"cycle_time\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n", "\n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(shipment * 3, shipment * 5)\n", "            初期在庫量リスト.append(random_inventory)\n", "\n", "        return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(solution, current_initial_inventory):\n", "    \"\"\"総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数\"\"\"\n", "    global 品番数, 期間\n", "    \n", "    # 最小化コスト\n", "    total_inventory_cost = 0\n", "    total_overtime_cost = 0\n", "    total_setup_cost = 0\n", "    total_shipment_delay_cost = 0\n", "    \n", "    inventory = current_initial_inventory[:]\n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    \n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            production = solution[t][i]\n", "            \n", "            # 生産がある場合に段替えをする\n", "            if production > 0:\n", "                daily_setup_count += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "            \n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory_history[i].append(inventory[i])\n", "            \n", "            # 出荷遅れコスト\n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount\n", "            \n", "            # 在庫コスト\n", "            if inventory[i] > 0:\n", "                total_inventory_cost += 在庫コスト単価 * inventory[i]\n", "        \n", "        # 段替えコスト\n", "        total_setup_cost += 段替えコスト単価 * daily_setup_count\n", "        \n", "        # 残業コスト\n", "        if daily_time > daily_regular_time:\n", "            overtime = daily_time - daily_regular_time\n", "            total_overtime_cost += 残業コスト単価 * overtime\n", "        \n", "        # 最大稼働時間超過ペナルティ（違反する場合はコストに加算）\n", "        if daily_time > daily_regular_time + max_daily_overtime:\n", "            work_time_penalty = (daily_time - (daily_regular_time + max_daily_overtime)) * (残業コスト単価 * 1000000)\n", "            total_overtime_cost += work_time_penalty\n", "            \n", "    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost\n", "    \n", "    return total_cost, inventory_history\n", "\n", "def generate_initial_solution(current_initial_inventory):\n", "    \"\"\"初期解を生成する関数\"\"\"\n", "    solution = []\n", "    temp_inventory = current_initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_productions = [0] * 品番数\n", "        daily_time = 0\n", "        \n", "        # 在庫がない品番を優先的に生産する\n", "        priorities = []\n", "        for i in range(品番数):\n", "            shortage_estimate = max(0, 出荷数リスト[i] - temp_inventory[i])\n", "            priorities.append((shortage_estimate, i))\n", "        \n", "        priorities.sort(key=lambda x: x[0], reverse=True)\n", "        \n", "        for shortage_estimate, i in priorities:\n", "            if shortage_estimate > 0:\n", "                setup_time = 30\n", "                remaining_time = max_daily_work_time - daily_time\n", "                \n", "                if remaining_time > setup_time:\n", "                    cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]\n", "                    if cycle_time_per_unit > 0:\n", "                        max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)\n", "                        \n", "                        if max_producible_by_time > 0:\n", "                            target_production = shortage_estimate + random.randint(0, 50)\n", "                            production = min(target_production, max_producible_by_time)\n", "                            \n", "                            daily_productions[i] = production\n", "                            daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "        \n", "        solution.append(daily_productions)\n", "        \n", "        for i in range(品番数):\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            \n", "    return solution\n", "\n", "def get_neighbors(current_solution):\n", "    \"\"\"近傍解を生成する関数\"\"\"\n", "    neighbors = []\n", "    \n", "    # 2つの生産量を入れ替える\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "        i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "        \n", "        if (t1, i1) != (t2, i2):\n", "            neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "            neighbors.append(neighbor)\n", "            \n", "    # 特定の生産量を増減させる\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t = random.randint(0, 期間 - 1)\n", "        i = random.randint(0, 品番数 - 1)\n", "        \n", "        change = random.randint(-50, 50)\n", "        neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "        neighbors.append(neighbor)\n", "        \n", "    return neighbors\n", "\n", "def local_search(initial_solution, current_initial_inventory):\n", "    \"\"\"ローカルサーチを実行する関数\"\"\"\n", "    current_solution = initial_solution\n", "    current_cost, _ = evaluate(current_solution, current_initial_inventory)\n", "    \n", "    while True:\n", "        neighbors = get_neighbors(current_solution)\n", "        best_neighbor = None\n", "        best_neighbor_cost = float('inf')\n", "        \n", "        for neighbor in neighbors:\n", "            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)\n", "            if neighbor_cost < best_neighbor_cost:\n", "                best_neighbor = neighbor\n", "                best_neighbor_cost = neighbor_cost\n", "        \n", "        if best_neighbor_cost < current_cost:\n", "            current_solution = best_neighbor\n", "            current_cost = best_neighbor_cost\n", "        else:\n", "            break\n", "            \n", "    return current_solution, current_cost\n", "\n", "def multi_start_local_search(num_starts, current_initial_inventory):\n", "    \"\"\"多スタートローカルサーチを実行する関数\"\"\"\n", "    best_solution_overall = None\n", "    best_cost_overall = float('inf')\n", "    \n", "    for i in range(num_starts):\n", "        print(f\"--- Start {i+1}/{num_starts} ---\")\n", "        \n", "        initial_solution = generate_initial_solution(current_initial_inventory)\n", "        \n", "        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory)\n", "        \n", "        if local_optimal_cost < best_cost_overall:\n", "            best_cost_overall = local_optimal_cost\n", "            best_solution_overall = local_optimal_solution\n", "            print(f\"  New best solution found with total cost: {best_cost_overall:.2f}\")\n", "            \n", "    return best_solution_overall, best_cost_overall\n", "\n", "def simulate_production_schedule_simple(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def optimize_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):\n", "    \"\"\"初期在庫量を最適化する関数（簡易シミュレーション使用版）\"\"\"\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    print(\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration+1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule_simple(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c)\n", "            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "            \n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する（負の値を防ぐ）\n", "        new_s = [max(0, s[i] - adjustments[i]) for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "\n", "def plot_results(best_individual, initial_inventory):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            production = best_individual[t][i]\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量', fontsize=18)\n", "    axes[0, 0].set_xlabel('期間', fontsize=18)\n", "    axes[0, 0].set_ylabel('総在庫量 (個)', fontsize=18)\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間（制限ラインを追加）\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間', fontsize=18)\n", "    axes[0, 1].set_xlabel('期間', fontsize=18)\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)', fontsize=18)\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "\n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数', fontsize=18)\n", "    axes[1, 0].set_xlabel('期間', fontsize=18)\n", "    axes[1, 0].set_ylabel('総段替え回数（回）', fontsize=18)\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量', fontsize=18)\n", "    axes[1, 1].set_xlabel('期間', fontsize=18)\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)', fontsize=18)\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "\n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period\n", "\n", "\n", "def main():\n", "    \"\"\"メイン実行関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "\n", "    result = read_csv('data.csv')\n", "    if result[0] is None:\n", "        print(\"CSVファイルの読み込みに失敗しました\")\n", "        return\n", "\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    \n", "    # 初期在庫量を更新処理\n", "    optimized_initial_inventory = optimize_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5)\n", "    初期在庫量リスト = optimized_initial_inventory\n", "\n", "    print(\"=== 多スタートローカルサーチ 生産スケジューリング最適化システム ===\")\n", "    \n", "    num_starts = 30\n", "    best_solution, best_cost = multi_start_local_search(num_starts, 初期在庫量リスト)\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "\n", "        plot_results(best_solution, 初期在庫量リスト)\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "861baf65", "metadata": {}, "source": ["変数名を揃える  \n", "パラメータの修正  "]}, {"cell_type": "markdown", "id": "24165dfe", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}