{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f57a500f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- 元の初期在庫 ---\n", "製品 A: 2653\n", "製品 B: 1165\n", "製品 C: 1911\n", "製品 D: 234\n", "製品 E: 3133\n", "製品 F: 48\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [600, 300, 500, 50, 1000, 10]\n", "  更新後の初期在庫量: [2053, 865, 1411, 184, 2133, 38]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [600, 300, 500, 50, 1000, 10]\n", "  更新後の初期在庫量: [1453, 565, 911, 134, 1133, 28]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [600, 300, 500, 50, 1000, 10]\n", "  更新後の初期在庫量: [853, 265, 411, 84, 133, 18]\n", "--- 調整イテレーション 4 ---\n", "  今回の調整量: [600, 265, 411, 50, 133, 10]\n", "  更新後の初期在庫量: [253, 0, 0, 34, 0, 8]\n", "--- 調整イテレーション 5 ---\n", "  今回の調整量: [253, 0, 0, 34, 0, 8]\n", "  更新後の初期在庫量: [0, 0, 0, 0, 0, 0]\n", "--- 最大反復回数に到達しました。---\n", "\n", "--- 調整後の初期在庫 ---\n", "製品 A: 0\n", "製品 B: 0\n", "製品 C: 0\n", "製品 D: 0\n", "製品 E: 0\n", "製品 F: 0\n", "Restricted license - for non-production use only - expires 2026-11-23\n", "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.5.0 24F74)\n", "\n", "CPU model: Apple M1\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Optimize a model with 340 rows, 520 columns and 1208 nonzeros\n", "Model fingerprint: 0x8d486fc1\n", "Variable types: 280 continuous, 240 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [2e-01, 1e+06]\n", "  Objective range  [7e+01, 5e+02]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [1e+01, 1e+03]\n", "Found heuristic solution: objective 2.583000e+08\n", "Presolve removed 60 rows and 6 columns\n", "Presolve time: 0.00s\n", "Presolved: 280 rows, 514 columns, 1122 nonzeros\n", "Variable types: 234 continuous, 280 integer (120 binary)\n", "\n", "Root relaxation: objective 1.457434e+03, 240 iterations, 0.00 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 1457.43432    0  140 2.5830e+08 1457.43432   100%     -    0s\n", "H    0     0                    2428520.0000 1457.43432   100%     -    0s\n", "H    0     0                    15600.000000 1457.43432  90.7%     -    0s\n", "     0     0     cutoff    0      15600.0000 15600.0000  0.00%     -    0s\n", "\n", "Cutting planes:\n", "  Implied bound: 40\n", "  MIR: 2\n", "  StrongCG: 1\n", "  Flow cover: 38\n", "  Network: 2\n", "  RLT: 1\n", "\n", "Explored 1 nodes (403 simplex iterations) in 0.02 seconds (0.01 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 3: 15600 2.42852e+06 2.583e+08 \n", "\n", "Optimal solution found (tolerance 1.00e-04)\n", "Best objective 1.560000000000e+04, best bound 1.560000000000e+04, gap 0.0000%\n", "Gurobi status= 2\n", "ステータス: Optimal\n", "総コスト: 15600.0\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 15600.00\n", "\n", "--- 生産スケジュール ---\n", "    Day_1   Day_2   Day_3   Day_4   Day_5   Day_6   Day_7   Day_8   Day_9  \\\n", "A   600.0   600.0   600.0   600.0   600.0   600.0   600.0   600.0   600.0   \n", "B   300.0   300.0   300.0   300.0   300.0   300.0   300.0   300.0   300.0   \n", "C   500.0   500.0   500.0   500.0   500.0   500.0   500.0   500.0   500.0   \n", "D    50.0    50.0    50.0    50.0    50.0    50.0    50.0    50.0    50.0   \n", "E  1000.0  1000.0  1000.0  1000.0  1000.0  1000.0  1000.0  1000.0  1000.0   \n", "F    10.0    10.0    10.0    10.0    10.0    10.0    10.0    10.0    10.0   \n", "\n", "   Day_10  Day_11  Day_12  Day_13  Day_14  Day_15  Day_16  Day_17  Day_18  \\\n", "A   600.0   600.0   600.0   600.0   600.0   600.0   600.0   600.0   600.0   \n", "B   300.0   300.0   300.0   300.0   300.0   300.0   300.0   300.0   300.0   \n", "C   500.0   500.0   500.0   500.0   500.0   500.0   500.0   500.0   500.0   \n", "D    50.0    50.0    50.0    50.0    50.0    50.0    50.0    50.0    50.0   \n", "E  1000.0  1000.0  1000.0  1000.0  1000.0  1000.0  1000.0  1000.0  1000.0   \n", "F    10.0    10.0    10.0    10.0    10.0    10.0    10.0    10.0    10.0   \n", "\n", "   Day_19  Day_20  \n", "A   600.0   600.0  \n", "B   300.0   300.0  \n", "C   500.0   500.0  \n", "D    50.0    50.0  \n", "E  1000.0  1000.0  \n", "F    10.0    10.0  \n", "\n", "=== 結果のプロット ===\n"]}, {"data": {"image/png": "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************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************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****************************************+XJN18880e477vvvv0+++/S5LS0tL02GOPKSQkRB06dFBWVpa+/PJLZWVl6fTp0ypRooQkqVKlSvrrr788bq9atWo6ePCgNm7cqHnz5mno0KFasGCBli5detH8BQcH6/Dhwxftc84XX3yhcePGubQ9//zzmjx5snbu3ClJ+uOPPxQSEqKePXtKOlvYqFSpkts+SpcurdDQ0FztFwAAAIGP+YV384sffvhBXbt2veR38yNHjshut2v37t2qXLmyJGnv3r1q2rSps8+5W2I99thjCg4Odlm/Q4cOLm1vvfWWOnTooOrVq6to0aK655579MMPP6hMmTKSpIULF7rk73wNGzbMMc7z5zGX66WXXlLJkiU9LtuyZYs+/fRTr7YPAHnFFRsA4GcjR47U559/rp49e+rxxx9XUlKSQkJCtG7dOj3zzDP64IMP3L7wN2zYUMYYNW3aVPPmzZMxRsYY1a5dWytXrtSUKVNyvf+aNWs6X127dnVZ1rVrV5flOZ3NlJmZqf79+6tOnTr63//+p9q1a+u3337Ts88+K7vdrn79+umOO+5wW/+ff/5R//79VaJECfXo0UMDBw7UnXfeKUnq27ev7rrrLhUuXFjR0dHKyMjQBx98oPT0dLVs2dLtqopWrVqpb9++atCggXNycOrUKRUpUkRFixZV0aJF3SY+5yxdulTJyclKTk7W9ddfry+++ELJycmaMWOGZs6cqeTkZH3xxReqVauWs19ORY3z3XLLLZo0aZL27t3rvOT9UnI6A+vCV9GiRV3WW7FihYYPH66HH35Yb775pqSzE9dGjRqpf//+SkhI0OrVqzVr1ixVqFDB5fXbb7/lKjYAAAAEPuYX3s8vHA6HsrKyLnmsxhhlZ2e7PM+jRo0aOnz4sA4fPqy9e/dq+/btkqTff//d2R4XFydJWrt2rbPt8OHD6tChgyQpNDRUH3/8scqUKaNZs2Y5t/3II4+ofPnyHl+bN292K5ycizE+Pl5ZWVk6deqUs33t2rWaNGmS5s+fL4fDccljLVmyZI5zk+LFi19yfQDwNa7YAAA/ycjIUL9+/fT5559ryZIluuOOO/TEE0/ozjvv1IgRIzRgwACNHj1ajz766GXvIy4uTps3b5YkJSYmKijIvZ597ou25H5G1dy5cy95RtWZM2fUpk0bJSQk6Ndff1XdunX19ddfq3DhwurcubMkaciQIXryySfVqFEjbdy40Xm2UO3atfXtt9/qkUce0csvv6zMzEwNHTpUf/75p5YuXapNmzZp0qRJatGihYKCgvTHH39oyZIl+s9//qNChQq5xLFt2zbnPXQrVqyoevXqad++fapQoYJbzIsWLdLPP/+sCRMmaOPGjS6XqJ88eVKPPvqobDabs/3nn3/WmTNnlJmZqWuuucY5Yfjss89yfGZFSkqKEhIStGfPHu3du1fJyckaNmyYQkIu/afXGJPjLaOCgoIUFhbm1h4VFaVGjRpJkl599VVnHmrUqCFJOnr0qPbs2aPdu3erUqVK+u9//6shQ4Zo27ZtuYoJAAAAgY35hW/nF74wcuRIHT16VLGxsSpXrpyzPTg4WLGxsYqKispx3dDQUP33v/9VeHi4s+3rr79W3bp19fDDD2vx4sUqXLiwZs+erc2bN+uNN95wWf/999/Xe++9p+3bt+vkyZMyxuiVV15R//791a1bN23atEn16tXTjz/+qJSUFPXq1ctjHEFBQbLb7YqJicnxRLGsrCyP8y4AuJL4lwwA8JODBw9q3bp1WrNmjfO+s/PmzVPz5s3Vq1cvdevWTS+++OIlt/P000/rmWeekXT2EuPzvfXWW5ozZ46ksxOPxx9/3G39c5c1S2cnERUrVnS+f/DBB11uUXTulk7nGzlypHbu3Kl169YpMjJS0tnJzLl/UJekYsWK6dNPP1WNGjU0duxYly/dDRs21HPPPaexY8dq9uzZmjRpkoYOHao//vhDRYsW1axZsxQbG6vjx4+rY8eOmjhxot5++223OFavXq39+/dLOvuQw3r16mnOnDkuEyebzab9+/drz5492rZtm6SzV1WcOzvrxx9/VLNmzTR27Fj16dNHRYoUca57bnIVGRmpb775Rtdcc41bDBs2bNDDDz+sxMREZWRkKDQ0VNWrV5d09vLsunXr5urh3fv379e1117rcdldd93l8eHmNWvW1PXXX+88e+3vv//WRx99pEaNGum1117T/PnzFR4erkqVKikkJETx8fGKjo6mqAEAAHCVYH5xlq/mF95KT0/XRx99pNdff10NGzZURkaGy8lLDRs2VGZmpjIzM51thQoVcimwnF/UkM4WGYoXL67SpUvrvffe04ABAzR79myPz/6oVKmSHnroITVp0kQbN27UmDFj1LhxY91yyy3q1q2b/vvf/6po0aJq166dNm3a5Lb+jh079MMPP0g6ewuy3Dh3ZU+7du1UqVKlXK0DAJeLW1EBgJ9Uq1ZNf/75p3PSkZmZqVGjRun333/XI488ok8//VSDBw/2eN/a833wwQc6ceKETpw4oVq1arksmzBhgjZv3qzNmzerb9++Htc/evSo85WSkuI8A2vz5s1KSUlxWX7+pOTcum+++aYmT57snHRIZy+zrl+/vkvfwoULq1OnTvriiy9c2pOTkzV37ly99dZbatWqlcaNG6etW7dKkiZOnKhrr71WQUFB2rJlix599FH16tXLufx8vXr10urVq7V69Wq9/PLLev3117V06VKXL+FNmzZVnz59NGXKFOdl3uesW7dOHTt21Pjx47VhwwZVrlxZX375pfbt26eOHTvq2LFj+uWXX1S2bFndcccdHm/fVKtWLQ0ePFhffvmltm7dqpMnT+qbb75RYmKiXn/99VwVNc53/Phx520AjDF69913c+y7bt063XbbbYqPj3f2+/HHH1W8eHGNGTNGs2bNUkZGhn799VdJ0tatW10mhwAAALA25hdn+Wp+8c8//zgLDYUKFdL9998vSS5tF3tGyLRp05Samqq9e/eqZMmSuXqdu/L6n3/+cd6e6lxx6fTp0/r6668VHR2tNWvWKCYmRmFhYdq5c6defPFFRUdHa8GCBc79t2jRQiNGjFDjxo1ls9mcz96bOnWqJk2a5Ly17bnb917ozJkzznGQ11d2dnaOeQEAX+E0TQDwI5vNJmOMvvzyS+dtilavXq1bb71Vv/76qwYPHqzKlSurU6dO6ty5sxo3bux2hn2fPn2cl3enpaVddizPPPOMPvroI7f2GjVqaMOGDR7XiY2NVZEiRdS2bVtnW3Z2tlatWuXxPrzXXXed4uLi5HA4FBQUpOeee07Tpk1TaGioRo8erc8//1zVq1fX1KlTtXnzZgUFBaly5cqaOnWqqlSponr16ik1NVXjxo3T3LlzXbZ9/n1wg4KC1KZNG7Vo0cL5ED/p7CTtgw8+cFlv7969Gjx4sFauXKk333zTeR/gZcuWqWrVqnrooYf0wAMPaPbs2brmmms0d+5cjRs3Ts2aNdP333+vJk2aOLdVuHBh9e/f3/k+MTFRLVq00LPPPqshQ4bkmHtfSElJ0YMPPqhXXnlFP/74oypVqqSSJUtqypQpWrdunebOnatu3bopNjZWjRo10oYNG/TUU09d0ZgAAACQv5hf+G5+UbJkSf3xxx/O97/++qs6derkcqutpKQkNWjQwC2ugwcP6t///rfsdrvGjBmjMWPG5Jinc8Wmc1fCSGeLEn/++accDodmzZql7t2769SpU+rRo4cmTZokSdq3b5/q1KmjQ4cO5bjtcw4dOqTk5GQtWLBA7dq1c1n2zz//qGzZsm7r1K5d23lrr0GDBrlcsZ6RkaGgoCCXq29OnTqlmTNnutzmFwCuKAMA8ItDhw6Zt99+29SoUcMUL17cTJw40WRkZLj1W758uXnkkUdMaGioKVasmJk9e7ZzWdOmTc28efOc72vXrm1WrlzpXFatWjVz++23m9tvv93Y7Xbz3HPPOftu27bNnP9noFu3biYmJsZl3ytXrjQ1atRwvq9cubKJjY11vh87dqypX7++yzpLly41oaGh5vjx427HMn78eHPNNdc436emppqkpCSTnZ1tihQpYvbu3Wv++OMP8+mnnzr7JCQkmKioKLNmzRpjjDHJyckmPT3dZbvdunUzkpyvzp07m/fff9+l7fzXiy++6Fw3KyvLTJs2zXz44Yc59r/wNXv2bJOUlOR2fOdLTk42devWNf37979ov3NWrlxpgoODjTHG7N2710hyy+G7775r7rrrLmOMMfPmzTOVK1f2uK17773XTJ8+3aVtwYIFZsOGDaZ8+fImPj7eBAcHm127duUqNgAAAAQ+5he+m194snLlSpObf0Y7ffq0adSokbn22mtN1apVjTHGxMTEmPDwcJdXjx49jDHG/Otf/zLdunXzuK0WLVqY2bNnm1OnThmbzWbi4+NN9erVjd1uN+XLlzc2m83Y7XZjt9tN9erVPW5j2bJlJiQkxDRr1szj8ipVqpiZM2fmeDyffvqpcw5yTrdu3cyECRNc2mrUqOHyswSAK41bUQGAH2RmZqpZs2aaPn26mjZtqpSUFA0ePFjh4eGy2Wwur+bNm+vbb7/VkSNHNGXKFD388MO52sepU6fUtGlTPfnkk3ryySd10003+fw4rrnmGrezuKZPn66HH37Y+QC/8y1fvly33HKL832xYsU0YcIE57Mxvv/+e/3nP/9Rx44dnX327dun7t2768EHH9Thw4dVuHBht3vNStLYsWOdt2z66KOP1L17d61fv16lSpXSrl27lJaWpt27d6t48eJ64oknnOsFBwfr2WefVdmyZVW6dGmXWz95ep27fdP59w6+0OnTp9WuXTv973//0+eff66yZct6vHXVpZw8edLlku6cHiguSePGjVOhQoUUGhqq5cuX64UXXnCOp0KFCun06dO69dZbVbFiRbVr1061atVS1apV8xwTAAAAAg/zi7N8Nb+oXbu21q5dm6uYP/roI7Vu3dr5fu7cuQoJCdG4ceOcbVlZWXryySeVnp6u9PR0jR492uXZGpfy999/Kzw8XOXLl9fff/+tuLg4rV27VoULF1ZcXJzi4uL0999/u623Y8cOtW/fXpGRkR4f9C6dvXVXRETERff/559/qm7dus7XokWLNHnyZJe2ffv25fp4AMAXKGwAgB+EhYVp/fr12rJli3r06CHp7D+Ge3p9//33ks5eCt21a1cVL15cgwcPls1m048//qguXbo4JylbtmzR3XffrWbNmik5OVnt27dXv3791K9fP4+XSF9o7NixKlOmjPP14IMPXrT/XXfdpd27d+vIkSOSpE2bNumbb77Rv/71L7e+3333nb7//nu32x+9++67zltI3XDDDVq8eLFOnz6tHj166IcfftB9992np59+Wo888oiSk5N18803a926dTnGlJaWpjVr1ujMmTOqV6+ennzyST3//PMKCwvTwIED1aVLF9WtW/eSubhcmZmZeuSRR7RlyxbNmzdP27dvl91ud7v3b25ER0erWLFizteAAQNy7Dts2DClp6erbdu2Gj16tNLT0/XVV1+pVq1aSk9PV5cuXSSd/Rlv2LBBvXv3vtxDBAAAQIBhfvF/fDG/iI+Pv+hJRec7ceKEM15J6t69u7755huFhYXlav3cWLp0qe666y716dNH5cuXV/ny5VW/fn2dOnXK+b58+fJq2bKlc53k5GS1bdtWt912m3r37i2bzZZj/OcKGwcOHHC5JdY5FStW1MiRI52vm2++Wffee69LW+nSpX12vACQGxQ2AMBPzj2s7ZzzH0J3/svTF+Jx48bp9OnTSkhI0Pvvv69Tp05p+fLlGj9+vE6fPq3//ve/2rt3r6KiovIU08svv+zyML+FCxdetH/Dhg11xx136IUXXtDevXvVpUsXde3a1WWSc+bMGU2dOlWPPfaY2rRpo+7duzuXJSYmKi0tTdHR0ZLOfmEuVaqU1q5dq19++cV55pTNZtP06dN13XXX6cknn9TQoUOd2xg+fLhWr16t6dOnKyoqSlFRURo2bJhOnDgh6ewDAk+fPq3atWvr+PHjznvSenLhAwI9vXbs2JHj+mfOnNHjjz+u7du367ffflPnzp1VokQJ1apVK0/3J65SpUqOV4ysXr06x/WWL1+ur7/+WsWLF9fJkyc1ffp0de7c2aXPsmXLJEnz58/XqVOnch0TAAAAAhvzC9/ML7wVFham4sWLu7XPmTPH+TN4+eWXc729M2fOaMaMGXrwwQf1/vvvOx8q/vvvv6tw4cLO94cPH9aSJUuc633++ec6efKkPv74Y+ezVzwJCQmRw+HQsWPH9Nhjj7mdkNWgQQP961//UnJysvOVmZmp06dPu7SNGTPG+eB6AMgPPDwcACwoJCREISEhGjp0qHbv3q2ePXuqQoUKeu2111S+fHlVrFhRYWFhql69ep62O2rUKL366qvO99nZ2S4P3/bkk08+Udu2bXXdddepXbt2mjp1qqSzX6R//PFHLVy4UElJSerfv79iYmJcLoFevXq1rr/+epcH0XXr1k3p6enas2ePbrzxRpd9Pfroo3rooYe0du1abdmyRbVr11bZsmVVtGhRNW7cWCNGjNB1113nso/Dhw8rODhYNptNx48f15o1a9S0aVOPx1K6dGkdPXr0osdbs2ZNj+3nihobN27Uzz//rEqVKjmXlS9fXhs2bNCpU6cUGhqqpKQkJSUl6eabb77ovvKqWbNmWrRokWbMmKFRo0bpxIkTmjhxonP53LlzNWXKFMXGxmrYsGFq27atFixYoFKlSvk0DgAAAFgL84v/m19cCYULF9bzzz+vyZMn53qdrKwsJSUlaeXKlWrZsqWefvrpPO2zT58+euihhxQZGSljjLKzsz32q1u3rp566imdPn1a1157rWbPnu2yvGfPntq1a5dL2/Hjx7VlyxatWbPGpf27777TV199lac4AeByUdgAgACR06XBkjw+UyImJkZffPGFNmzYoKCgINWqVUtvvPGGZsyYocqVK6tFixYKDw/XTz/9pHLlyunAgQMqUqRIjvu49tpr9d5776lbt24u7cnJyVqzZo2uueYaJScnKzg42GW53W7XH3/8oeTkZJf73h4+fFhbt25V37591aVLF5d/6D8nKCjI5X60545rwYIFqlOnjkqVKiWbzaYdO3bo1KlT2rRpk3r37q0BAwY4Lw0fOHCg/ve//6lq1aqqVq2aczsHDx7UzJkz9fbbb2vYsGEaMmSIZs+erbZt2+rGG29U165d1adPH5d9//PPPxf9OVzMW2+9pTVr1mj16tVux/rUU0/pyy+/dMl/tWrVtHPnTpd+2dnZedr/hZPC4OBgXXvttQoJCVGZMmXUvHlz1a1bV7Nnz9b69ev11ltv6aOPPlLLli1Vt25dNWvWTDfffLO++OIL3X777Zdx1AAAAAhUzC/+77jyMr845+677/Z4XJ7yWq9evRzzIEmDBg1yeX/w4EHFx8erbNmy2rt3r9sVHkePHlXnzp0VERGhvXv3KjU1VStWrFDNmjVVunRphYeHq2jRonrnnXeUmJiokydPKi0tTcnJyapdu7bztlDlypVzbvPc7bkuNG/ePM2aNUvVq1dX586dFRoa6rJ81apVbut0795dderU0eDBgy963ABwReX308oBAK7Wrl1rJJkzZ854fC1btsyEh4e7rJOSkmIqVapkFi1a5NLucDjM/v37TeHChc3atWuNMcbUqFHDFCpUyBQtWtR89dVXzr7btm0zufkzEBcXZ2rWrGkqVqxo6tWrZxITE31w1O6KFCli9u7d63y/c+dOY4wxTzzxhAkPDzfh4eGmbt265tixY27rduvWzYwdO9b5/vTp06Zq1armkUceMRs3bnTpe/ToUTNixAgTExPj0h4bG2tKly59yThr1KhhZs+e7daelZVl9uzZc9F1Dx8+bHbu3GmOHDlizpw547Js5cqVJjg42CQlJeXq9e6775rKlSs7109MTDRVq1Y1ZcuWNcOGDTMnT540xhjz448/mkWLFpnIyEizbNkyt1y0bNnS7Nq165LHDQAAAGtgfnGWN/OL4sWLm2+++SZX38snTpxo6tWr57aNr7/+2lStWtVjbCtXrjTFixc3wcHBpnDhwmbOnDkuy59++mlzzz33mJMnT5qTJ0+aUaNGmdtuu82UKlXKBAUFGUlur5CQEFOmTBlz/Phxt/399ttv5osvvshD9oyZOnWqKVKkiMdXSEiICQsLy3H5Rx99lKd9AcDlsBmTw032AAAB7eTJkzmeIbVnzx5dd911+RxRYMnIyPB4JtrVbNu2bapevbrbWW/S2YcCXnjfZQAAAOAc5he+dfLkSf3zzz8eryy5lMOHD6t48eIut9Q6xxij06dPO6/ACA4OVqFChTzOAQDgakZhAwAAAAAAAAAAWEbQpbsAAAAAAAAAAAAEBgobAAAAAAAAAADAMihsAAAAAAAAAAAAy6CwAQAAAAAAAAAALCPE3wFYlcPhUEJCgooVKyabzebvcAAAAIB8Z4xRWlqaoqKiFBTEOVPeYo4BAACAgiwv8wsKG5cpISFBFStW9HcYAAAAgN8dPHhQ0dHR/g7D8phjAAAAALmbX1DYuEzFihWTdDbJERERuVrH4XAoKSlJkZGRnNF2mcih98ihb5BH75FD75FD3yCP3iOH3rNqDlNTU1WxYkXnd2N4J69zDKuOm0BDHr1HDr1HDn2DPHqPHHqPHHqPHPqGFfOYl/kFhY3LdO7S8IiIiDwVNtLT0xUREWGZwRRoyKH3yKFvkEfvkUPvkUPfII/eI4fes3oOuW2Sb+R1jmH1cRMoyKP3yKH3yKFvkEfvkUPvkUPvkUPfsHIeczO/sNYRAQAAAAAAAACAAo3CBgAAAAAAAAAAsAwKGwAAAAAAAAAAwDIobAAAAAAAAAAAAMugsAEAAAAAAAAAACyDwgYAAAAAAAAAALAMChsAAAAAAAAAAMAyKGwAAAAAAAAAAADLoLABAAAAAAAAAAAsg8IGAAAAAAAAAACwDAobAAAAAAAAAADAMihsAAAAAAAAAAAAy6CwAQAAAAAAAAAALIPCBgAAAAAAAAAAsAwKGwAAAAAAAAAAwDIobAAAAAAAAAAAAMso0IWNPXv26MEHH1SFChUUFRWlDh066NChQ/4OCwAAAAAAAAAA5KDAFjaOHz+upk2bqlGjRoqLi9OePXsUHh6ut99+29+hAQAAAAAAAACAHIT4OwB/mTRpkq677jq9+OKLkqTg4GDNnj1bwcHBfo4MAAAAAAAAAADkpMBesfHf//5XDz/8sEsbRQ0AAAAAAAAAAAJbgb1iY9euXSpbtqx69uyplStXqlixYurQoYOGDh2qkBD3tGRkZCgjI8P5PjU1VZLkcDjkcDhytU+HwyFjTK77wx059B459A3y6D1y6D1y6Bvk0Xvk0HtWzaHV4gUAAABwdSiwhY3s7GyNGjVK7777rmbOnKmdO3fqkUce0bFjx/TGG2+49Y+JidGYMWPc2pOSkpSenp6rfTocDqWkpMgYo6Ag7y+Wyf5pqdfbyI3gJvcFTAwX5rAg5sDb/TskpQaFKMuRladLtq6mHPhi/4E2FvNr/76MgbHo/f49/V0paDnwRQy+HotWzIG3MQTaZ6Lk/5+Dv8dhfklLS/Pr/gEAAAAUTAW2sFGpUiV17dpV9957rySpevXqevnll9WvXz+PhY3hw4dr0KBBzvepqamqWLGiIiMjFRERkat9OhwO2Ww2RUZG+qSwkZWd6fU2ciOkbNmAieHCHBbEHHi7f4ckm6Qy2Zl5+oeTqykHvth/oI3F/Nq/L2NgLHq/f09/VwpaDnwRg6/HohVz4G0MgfaZKPn/5+DvcZhfChUq5Nf9AwAAACiYCmxho3HjxsrMdJ9whoeHe+wfHh7ucVlQUFCeihQ2my3P6+Qkvx6QcrFY/RHD+TksqDnwdv+2/79eXta92nLgi/0H0ljMzwcmMRb9/3PI6TPRXzG4tPt5/5cbgy/HolVz4G0MgfSZKPn/5+DvcZhf/L1/AAAAAAVTgZ2JDBs2TDNmzNCyZcskSQcPHtTYsWPVs2dPP0cGAAAAAAAAAAByUmALG9WqVdPnn3+uESNGqGzZsmrWrJk6dOigV155xd+hAQAAAAAAAACAHBTYW1FJUtOmTfXbb7/5OwwAAAAAAAAAAJBLBfaKDQAAAAAAAAAAYD0UNgAAAAAAAAAAgGVQ2AAAAAAAAAAAAJZBYQMAAAAAAAAAAFgGhQ0AAAAAAAAAAGAZFDYAAAAAAAAAAIBlUNgAAAAAAAAAAACWQWEDAAAAAAAAAABYBoUNAAAAAAAAAABgGRQ2AAAAAAAAAACAZVDYAAAAAAAAAAAAlkFhAwAAAAAAAAAAWAaFDQAAAAAAAAAAYBkUNgAAAAAAAAAAgGVQ2AAAAAAAAAAAAJZBYQMAAAAAAAAAAFgGhQ0AAAAAAAAAAGAZFDYAAAAAAAAAAIBlUNgAAAAAAAAAAACWQWEDAAAAAAAAAABYBoUNAAAAAAAAAABgGRQ2AAAAAAAAAACAZVDYAAAAAAAAAAAAlkFhAwAAAAAAAAAAWAaFDQAAAAAAAAAAYBkUNgAAAAAAAAAAgGVQ2AAAAAAAAAAAAJZBYQMAAAAAAAAAAFgGhQ0AAAAAAAAAAGAZFDYAAAAAAAAAAIBlUNgAAAAAAAAAAACWQWEDAAAAAAAAAABYBoUNAAAAAAAAAABgGRQ2AAAAAAAAAACAZVDYAAAAAAAAAAAAlkFhAwAAAECBNGfOHNWpU0fR0dGqX7++Vq9enWPf+Ph4dejQQVWqVJHdbtfAgQOVkZHhsW9WVpYaNGigKlWqXKHIAQAAgIKNwgYAAACAAmfevHkaPny4FixYoLi4OA0ZMkStW7fWnj173PpmZmbqvvvuU3R0tHbt2qUtW7Zow4YNGjhwoMdtv/LKKzkWPQAAAAB4j8IGAAAAgAJnzJgxGjx4sGrVqiVJat++vZo0aaIpU6a49Z0/f76OHDmimJgYhYSEqESJEpo8ebJmzpypo0ePuvT96aefNG/ePI0dOzZfjgMAAAAoiChsAAAAAChQDhw4oN27d6tt27Yu7W3btlVsbKxb/xUrVqhFixYKCwtzttWrV0+lS5fW8uXLnW3Jycnq0qWLpk6dqoiIiCt3AAAAAEABF+LvAAAAAAAgPyUkJEiSoqKiXNrtdrvi4+M99q9Tp45b+4X9n3nmGd1///1q166dVq1adck4MjIyXG5ZlZqaKklyOBxyOByXXN/hcMgYk6u+yBl59B459B459A3y6D1y6D1y6D1y6BtWzGNeYqWwAQAAAKBACQ0NlSQFBblewG6z2XLsf2HfC/vPmTNH69ev159//pnrOGJiYjRmzBi39qSkJKWnp19yfYfDoZSUFBljPMaH3CGP3iOH3iOHvkEevUcOvUcOvUcOfcOKeUxLS8t1XwobAAAAAAqU6OhoSWevxKhWrZqz/dChQ7Lb7R77n7vK43zn+u/Zs0cDBw7Ud999p6JFi+Y6juHDh2vQoEHO96mpqapYsaIiIyNzdSsrh8Mhm82myMhIy0xWAxF59B459B459A3y6D1y6D1y6D1y6BtWzGOhQoVy3ZfCBgAAAIACpVy5cqpbt66+++479e/f39m+dOlStWrVyq1/y5Yt1atXL2VlZSkk5OwUavv27UpMTFTz5s21ePFiJScn684773Rb12azacSIEXr11VfdloWHhys8PNytPSgoKNeTT5vNlqf+8Iw8eo8ceo8c+gZ59B459B459B459A2r5TEvcVrjiAAAAADAh4YMGaLx48drx44dkqSFCxcqNjZWffv2devbunVrlS1bVi+//LKys7OVkpKifv36qUePHipTpoy6d+8uY4zLa+XKlapcubKMMR6LGgAAAAAuH1dsAAAAAChwOnXqpNTUVLVp00YnTpxQdHS0Fi9erGrVqikuLk4NGzbU5MmT1b59e4WEhGjJkiV67rnnVLFiRQUFBal9+/YaN26cvw8DAAAAKJAobAAAAAAokPr06aM+ffq4tUdHRysuLs6tbeHChbnedrNmzbRv3z5vQwQAAADgAbeiAgAAAAAAAAAAlkFhAwAAAAAAAAAAWAaFDQAAAAAAAAAAYBkUNgAAAAAAAAAAgGVQ2AAAAAAAAAAAAJZBYQMAAAAAAAAAAFgGhQ0AAAAAAAAAAGAZFDYAAAAAAAAAAIBlUNgAAAAAAAAAAACWQWEDAAAAAAAAAABYBoUNAAAAAAAAAABgGRQ2AAAAAAAAAACAZVDYAAAAAAAAAAAAlkFhAwAAAAAAAAAAWAaFDQAAAAAAAAAAYBkUNgAAAAAAAAAAgGVQ2AAAAAAAAAAAAJZBYQMAAAAAAAAAAFgGhQ0AAAAAAAAAAGAZFDYAAAAAAAAAAIBlUNgAAAAAAAAAAACWQWEDAAAAAAAAAABYBoUNAAAAAAAAAABgGRQ2AAAAAAAAAACAZVDYAAAAAAAAAAAAllGgCxsbNmxQaGiooqOjXV5ff/21v0MDAAAAAAAAAAAehPg7AH+Ki4tT/fr1tWbNGn+HAgAAAAAAAAAAcqFAX7ERFxenihUr+jsMAAAAAAAAAACQSwW+sBEdHe3vMAAAAAAAAAAAQC4V+FtRhYSE6KGHHtJff/2l0qVL69lnn1XPnj3d+mZkZCgjI8P5PjU1VZLkcDjkcDhytT+HwyFjTK77X3J7PtlKLvZzkXjzO4YLc1gQc+Dt/h2SzOWsdxXlwBf7D7SxmF/792UMjEXv9+/p70pBy4EvYvD1WLRiDryNIdA+EwMhBn+Pw/zi7/0DAAAAKJgKdGHDZrMpMTFRU6dOVeXKlbV+/Xo9+OCDOnPmjPr06ePSNyYmRmPGjHHbRlJSktLT03O1P4fDoZSUFBljFBTk/cUy2cFhXm8jN4ITEwMmhgtzWBBz4O3+HZJSg0JklLdLtq6mHPhi/4E2FvNr/76MgbHo/f49/V0paDnwRQy+HotWzIG3MQTaZ6Lk/5+Dv8dhfklLS/Pr/gEAAAAUTAW6sDF37lyX9/Xr19cLL7yg2bNnuxU2hg8frkGDBjnfp6amqmLFioqMjFRERESu9udwOGSz2RQZGemTwkZWdqbX28iNkLJlAyaGC3NYEHPg7f4dkmySymRn5ukfTq6mHPhi/4E2FvNr/76MgbHo/f49/V0paDnwRQy+HotWzIG3MQTaZ6Lk/5+Dv8dhfilUqJBf9w8AAACgYCrQhQ2Hw+FWYMjOzpbNZnPrGx4ervDwcLf2oKCgPBUpbDZbntfJSX49IOVisfojhvNzWFBz4O3+bf9/vbyse7XlwBf7D6SxmJ8PTGIs+v/nkNNnor9icGn38/4vNwZfjkWr5sDbGALpM1Hy/8/B3+Mwv/h7/wAAAAAKpgI9E2ndurVefPFFnTp1SpK0fv16vfnmm+rVq5efIwMAAAAAAAAAAJ4U6MLG+++/ryNHjqhGjRoqV66cnnjiCY0ePdrjw8MBAAAAAAAAAID/FehbUUVHR7s9ZwMAAAAAAAAAAASuAn3FBgAAAAAAAAAAsBYKGwAAAAAAAAAAwDIobAAAAAAAAAAAAMugsAEAAAAAAAAAACyDwgYAAAAAAAAAALAMChsAAAAAAAAAAMAyKGwAAAAAAAAAAADLoLABAAAAAAAAAAAsg8IGAAAAAAAAAACwDAobAAAAAAAAAADAMihsAAAAAAAAAAAAy6CwAQAAAAAAAAAALIPCBgAAAAAAAAAAsAwKGwAAAAAAAAAAwDIobAAAAAAAAAAAAMugsAEAAAAAAAAAACyDwgYAAAAAAAAAALAMChsAAAAAAAAAAMAyKGwAAAAAAAAAAADLoLABAAAAAAAAAAAsg8IGAAAAAAAAAACwDAobAAAAAAAAAADAMihsAAAAAAAAAAAAy6CwAQAAAAAAAAAALIPCBgAAAAAAAAAAsAwKGwAAAAAAAAAAwDIobAAAAAAAAAAAAMugsAEAAAAAAAAAACyDwgYAAAAAAAAAALAMChsAAAAAAAAAAMAyKGwAAAAAAAAAAADLoLABAAAAAAAAAAAsg8IGAAAAAAAAAACwDAobAAAAAAAAAADAMihsAAAAAAAAAAAAy6CwAQAAAAAAAAAALIPCBgAAAAAAAAAAsAwKGwAAAAAAAAAAwDIobAAAAAAAAAAAAMugsAEAAAAAAAAAACyDwgYAAAAAAAAAALAMChsAAAAAAAAAAMAyKGwAAAAAAAAAAADLoLABAAAAAAAAAAAsg8IGAAAAAAAAAACwDAobAAAAAAAAAADAMihsAAAAAAAAAAAAy6CwAQAAAKBAmjNnjurUqaPo6GjVr19fq1evzrFvfHy8OnTooCpVqshut2vgwIHKyMhw6fPLL7+oSZMmio6O1rXXXqsXXnhBJ0+evNKHAQAAABQ4FDYAAAAAFDjz5s3T8OHDtWDBAsXFxWnIkCFq3bq19uzZ49Y3MzNT9913n6Kjo7Vr1y5t2bJFGzZs0MCBA519tm/frtatW+uFF15QXFycNm7cqE2bNmnAgAH5eFQAAABAwUBhAwAAAECBM2bMGA0ePFi1atWSJLVv315NmjTRlClT3PrOnz9fR44cUUxMjEJCQlSiRAlNnjxZM2fO1NGjRyVJ69atU48ePfToo49KkkqUKKGBAwfqyy+/zL+DAgAAAAoIChsAAAAACpQDBw5o9+7datu2rUt727ZtFRsb69Z/xYoVatGihcLCwpxt9erVU+nSpbV8+XJJUteuXTV58mSX9TZt2qSIiIgrcAQAAABAwRbi7wAAAAAAID8lJCRIkqKiolza7Xa74uPjPfavU6eOW3tO/SVp7ty5GjNmjGbMmJFjHBkZGS7P6UhNTZUkORwOORyOSx6Hw+GQMSZXfZEz8ug9cug9cugb5NF75NB75NB75NA3rJjHvMRKYQMAAABAgRIaGipJCgpyvYDdZrPl2P/Cvjn1P336tJ577jl9/fXXmjdvnh5//PEc44iJidGYMWPc2pOSkpSenn7RY5DOTvxSUlJkjPEYH3KHPHqPHHqPHPoGefQeOfQeOfQeOfQNK+YxLS0t130pbAAAAAAoUKKjoyWdvRKjWrVqzvZDhw7Jbrd77H/uKo/zXdj/n3/+UYsWLRQZGaktW7a4XRFyoeHDh2vQoEHO96mpqapYsaIiIyNzdQsrh8Mhm82myMhIy0xWAxF59B459B459A3y6D1y6D1y6D1y6BtWzGOhQoVy3ZfCBgAAAIACpVy5cqpbt66+++479e/f39m+dOlStWrVyq1/y5Yt1atXL2VlZSkk5OwUavv27UpMTFTz5s0lSWfOnFGbNm3UqFEjTZ48OcerP84XHh6u8PBwt/agoKBcTz5tNlue+sMz8ug9cug9cugb5NF75NB75NB75NA3rJbHvMRpjSMCAAAAAB8aMmSIxo8frx07dkiSFi5cqNjYWPXt29etb+vWrVW2bFm9/PLLys7OVkpKivr166cePXqoTJkykqTJkyercOHCuS5qAAAAALh8XLEBAAAAoMDp1KmTUlNT1aZNG504cULR0dFavHixqlWrpri4ODVs2FCTJ09W+/btFRISoiVLlui5555TxYoVFRQUpPbt22vcuHHO7cXGxmrjxo2qWLGi274WLFigO+64Iz8PDwAAALiqUdgAAAAAUCD16dNHffr0cWuPjo5WXFycW9vChQtz3NbKlSt9Hh8AAAAAz7gVFQAAAAAAAAAAsAwKGwAAAAAAAAAAwDIobAAAAAAAAAAAAMugsAEAAAAAAAAAACyDwgYAAAAAAAAAALAMChsAAAAAAAAAAMAyKGwAAAAAAAAAAADLoLABAAAAAAAAAAAsg8IGAAAAAAAAAACwDAobAAAAAAAAAADAMihsAAAAAAAAAAAAy6CwAQAAAAAAAAAALIPCBgAAAAAAAAAAsAwKG5L279+vEiVKqHv37v4OBQAAAAAAAAAAXESBL2w4HA516dJFlStX9ncoAAAAAAAAAADgEgp8YeO1115TRESEHn74YX+HAgAAAAAAAAAALqFAFzbWrVunt956S9OmTfN3KAAAAAAAAAAAIBdC/B2Av5w4cUJPPPGE3nzzTVWqVOmS/TMyMpSRkeF8n5qaKunsrawcDkeu9ulwOGSMyXX/S27PJ1vJxX4uEm9+x3BhDgtiDrzdv0OSuZz1rqIc+GL/gTYW82v/voyBsej9/j39XSloOfBFDL4ei1bMgbcxBNpnYiDE4O9xmF/8vX8AAAAABVOBLWz069dPt912mzp37pyr/jExMRozZoxbe1JSktLT03O1DYfDoZSUFBljFBTk/cUy2cFhXm8jN4ITEwMmhgtzWBBz4O3+HZJSg0JklLdLtq6mHPhi/4E2FvNr/76MgbHo/f49/V0paDnwRQy+HotWzIG3MQTaZ6Lk/5+Dv8dhfklLS/Pr/gEAAAAUTAWysLFgwQItW7ZMmzZtyvU6w4cP16BBg5zvU1NTVbFiRUVGRioiIiJX23A4HLLZbIqMjPRJYSMrO9PrbeRGSNmyARPDhTksiDnwdv8OSTZJZbIz8/QPJ1dTDnyx/0Abi/m1f1/GwFj0fv+e/q4UtBz4IgZfj0Ur5sDbGALtM1Hy/8/B3+MwvxQqVMiv+wcAAABQMBXIwsa3336r+Ph4lSpVym3Zhx9+qKVLl+ree+91aQ8PD1d4eLhb/6CgoDwVKWw2W57XyUl+PSDlYrH6I4bzc1hQc+Dt/m3/f728rHu15cAX+w+ksZifD0xiLPr/55DTZ6K/YnBp9/P+LzcGX45Fq+bA2xgC6TNR8v/Pwd/jML/4e/8AAAAACqYCOROZM2eOjDEur1GjRqlbt24yxrgVNQAAAAAAAAAAQGAokIUNAAAAAAAAAABgTQXyVlSejB492t8hAAAAAAAAAACAS+CKDQAAAAAAAAAAYBkUNgAAAAAAAAAAgGVQ2AAAAAAAAAAAAJZBYQMAAAAAAAAAAFgGhQ0AAAAAAAAAAGAZFDYAAAAAAAAAAIBlUNgAAAAAAAAAAACWQWEDAAAAAAAAAABYBoUNAAAAAAAAAABgGRQ2AAAAAAAAAACAZVDYAAAAAOBXP/zwg3r16qVatWqpRIkSKlq0qKpWrapOnTrps88+kzHG3yECAAAACCAh/g4AAAAAQMG0Z88ede3aVdnZ2WrXrp06deqkcuXKKSQkRElJSfrjjz80d+5cjR07Vu+//77uvPNOf4cMAAAAIABYprBhjNGqVasUGxurXbt26dixYypVqpSqVaumVq1aqVmzZrLZbP4OEwAAAEAuHDlyRD179tTrr7+uu+66y215jRo11KhRI/Xv3187duzQoEGD9Morr6hBgwZ+iBYAAABAILFEYSM2NlaDBw9W0aJF1apVKz366KMqVaqUjh07pl27dmnYsGE6efKkJkyYoFatWvk7XAAAAACXUK5cOS1btkwhIZeeklSvXl2LFy9WdnZ2PkQGAAAAINAFfGHjpZde0urVqzVr1izdfvvtHvuMGjVK69at04svvqjVq1frP//5Tz5HCQAAACAv5s6dm6f+Xbt2VXBw8BWKBgAAAICVBHxhIzMzU6tWrVJQ0MWfc96gQQOtXLlSQ4cOzafIAAAAAFyuAQMG6Omnn5YxRjabLccHhH/wwQfq1atXPkcHAAAAIJAFfGFj4sSJue4bFBSkCRMmXMFoAAAAAPhC6dKlNX78eEnSDTfcoK1bt0qSFi1a5Oxz55136osvvnD2AwAAAADJAoUNAAAAAFcfm83msf2pp55Sx44d9ddff6lIkSI59gMAAABQcFmisPHSSy/lqf9rr712hSIBAAAA4Evdu3fXoUOH1LVrVxljVKFCBb3zzjt66623/B0aAAAAgAB18QdXBIipU6cqPDz8kq9p06apUKFC/g4XAAAAQC517dpVxYsXV/fu3dWtWzdJksPhyPGZGwAAAABgiSs2IiIiNGrUqEv2++CDD/TKK6/kQ0QAAAAAvHGucHHPPffommuu0T333CNJSk1NVdWqVWWz2fTRRx/5M0QAAAAAAcoShY3z76sbFRWlo0ePuiy//vrrtWXLlvwOCwAAAMBlstlsMsbIGKPevXs7/3/Pnj0yxjiXS3K+BwAAAADJIoWN8y9DDw8PV2Zmpsvy6tWr53dIAAAAALyQlpYmu93u/K4/fvx4Sa4nNZ1bZrfbFR8fT3EDAAAAgCSLFDbOn8AwmQEAAACsbcaMGRo9erTatm2rqKgof4cDAAAAwGIsUdgAAAAAcPWoWbOmFixYoNdff10VKlSQ3W6/5Drz58/Ph8gAAAAAWIHlChvZ2dlau3aty/12AQAAAFhH06ZN1bRpU0nSTz/9pBkzZujLL7/U448/rq5du3KVNgAAAICLskRh4/ziRd26dfXSSy+5LL/99tvzOyQAAAAAPtCkSRM1adJEMTExGjFihJYsWaKJEyf6OywAAAAAAcwShY3zz9hauHBhrvoBAAAACGwOh0NBQUGSpEqVKmnevHkX7W+M4Ts/AAAAAGsUNo4dO6aePXvmut+sWbPyISoAAAAAl+vIkSPq1KmTxo0bpwYNGly07969ezVo0CANGzaMq7UBAAAAWKOw8cYbb+SqH5McAAAAwBrKlSund999V927d1dwcLAefPBBNWjQQOXKlVNQUJD++ecfbdy4UbGxsdq1a5dmzJjB930AAAAAkixS2OjTp4+/QwAAAADgYzVq1NDatWsVGxur+fPn6/3339ehQ4fkcDhUtmxZ3XbbberYsaM6duyo4OBgf4cLAAAAIEBYorABAAAA4OrVqlUrtWrVyt9hAAAAALCIIH8HcCmPPfaYTp8+nau+p06dUvv27a9wRAAAAAAAAAAAwF8CvrDRsmVL1atXT59//rmMMTn2++yzz1S/fn3O9AIAAAAAAAAA4CoW8Leievrpp1W/fn0NHz5czz//vFq0aKHrr79epUuX1rFjx7Rr1y59//33qlevnj799FPddNNN/g4ZAAAAAAAAAABcIQFf2JCkm2++Wd9995327dun2NhY7d69W/v371fJkiV1xx136N///rcqV67s7zABAAAAAAAAAMAVZonCxjlVqlTRs88+6+8wAAAAAPjYTz/9pCZNmnhc9t5776l37975HBEAAACAQBXwz9gAAAAAcPX77LPP3Nr27dsnSZo+fXo+RwMAAAAgkFnqig0AAAAAV4f33nvP+f8VKlSQMUaDBg3Sxo0bZbPZdOONN+r333/XmjVrZIzxY6QAAAAAAg1XbAAAAADId6NHj9Zrr72mvXv3asKECZKkP//8U1OmTNGJEydUrFgxP0cIAAAAIFBR2AAAAACQ7+x2u6677jrFxMS4XJFRu3ZtFSlShMIGAAAAgBxR2AAAAAAQcGw2m8f/BwAAAACesQEAAADA72w2m4wxuv/++/W///1PCQkJSkxM1H333afdu3f7OzwAAAAAAYQrNgAAAAD4nTFGNptNkydPVrVq1dS6dWtVqVJFb775pqKiovwdHgAAAIAAQmEDAAAAQMA494yNcuXK6ZprrlHt2rVVqFAhf4cFAAAAIIBYprARHx+vZcuWXbTPnj17NH/+/HyKCAAAAMDlOnPmjDIyMpSQkOBxOc/VAAAAAJATyxQ2Nm/erFGjRunvv/9WZmam2/LMzEx16NBBq1atyv/gAAAAAORJenq6jh07pnvvvVeVK1eWdPZ2VDfeeKN+//13/fjjj36OEAAAAECgstTDw/fs2aOnn35ae/fuVWhoqJo3b66OHTuqYcOGevTRR1W1alVNmTLF32ECAAAAuITt27e7vH/22Wf11VdfOU9iCgsL04IFC/wRGgAAAIAAZ6nCRv369bVo0SJJ0uHDh/X999/riSeeUEpKim6//XZ9//33fo4QAAAAwOW47rrrVLJkSZe23r17S5KuvfZaf4QEAAAAIEAFfGGjZ8+eMsYoKytLKSkpOnXqlBISErRkyRJ98MEHeuCBB3Tvvfdq8ODBGjlypF599VV/hwwAAAAgj1588cUcl3311Vf5GAkAAACAQBfwhY0+ffpo06ZNWr9+vY4ePapixYrJZrPpmWee0YoVK1SqVClJ0j333KN7771XERERGjJkiJ+jBgAAAOCNV199VcYYvfzyy/4OBQAAAECACfiHh9erV09PP/20pk+frv79+2vbtm3q1auXNm7cqH379mno0KGSpKioKH399df69NNPlZGR4eeoAQAAAHjjySef1IIFCzRp0iR/hwIAAAAgwAR8YWPmzJm68cYb9eWXX2rGjBkqVaqUdu3apV9++UW33nqr5s6dq4ULFyorK0uffPKJRo4cqfDwcH+HDQAAACCX0tPT3U5OqlKlij788EONHz/eT1EBAAAACFSWuBXVjTfeqF27dskYo6lTp2rUqFHO5cHBwfrmm280bNgwnTp1Shs3bvRjtAAAAADy4tlnn9V7770nSSpatKgiIiJUtGhRFSlSRP/8849q1Kjh5wgBAAAABJqAL2xERkbKZrPJGKPk5GT9/fffmjp1qho3bqxOnTqpbNmymj17tkaPHq133nlHp0+f9nfIAAAAAHJpwYIFOnjwoEqXLq3jx48rJSVFaWlpSk9PV0REhG688UZ/hwgAAAAgwAR8YSMpKUlHjhzRzp079fzzzys6OloTJ07U/v379dVXX2nHjh16/fXX9fXXXysmJkb/+te/9Nlnn/k7bAAAAAC5cNttt2nTpk1q0aKFypcvr/Lly/s7JAAAAAABLuCfsbFy5UrVqlVLsbGxstlsmj59ujp27KgGDRpo+vTpqlSpkk6cOKHbbrtNvXv31saNG5WSkuLvsAEAAADkwr///W8NHDhQx48f93coAAAAACwi4K/YuPPOO7Vnzx6VKFFCO3bskN1uV5cuXbRo0SJ17dpV1157rcaOHevs/8orryg4ONiPEQMAAADIrdq1a+uFF17QAw88oO+//14RERH+DgkAAABAgAv4wkZ4eLjCw8Mlnb3/riQNGjTIufzbb7916d+5c+f8Cw4AAADAZRk5cqRKliypzz77TEWLFlVoaKgaN26swYMHO09Ucjgcys7OVtu2bVWqVCmfxzBnzhxNnDhRycnJqlChgiZPnqxGjRp57BsfH69Bgwbpt99+05kzZ/T4449r3LhxzrmKJP3666968cUXtX//foWHh+vFF19U7969fR43AAAAUNAFfGEDAAAAwNUnJCRESUlJOnnypEqWLKmqVasqOTlZ3bt3V5UqVVS/fn1lZ2fLZrOpadOmPi9szJs3T8OHD9eKFStUq1YtLViwQK1bt9bGjRt13XXXufTNzMzUfffdp1atWunjjz/WiRMn1K5dOw0cOFDTpk2TJG3fvl3333+/Zs+erUcffVRbt27VPffcoxIlSujxxx/3aewAAABAQWeJwkZUVFSe+ickJFyhSAAAAAD4wujRoyVJhQoV0unTp/X6669Lkv773/+qZ8+eio6OVkxMjEJDQ6/I/seMGaPBgwerVq1akqT27dtr7ty5mjJliiZNmuTSd/78+Tpy5IhiYmIUEhKiEiVKaPLkybrzzjv173//W2XKlNHEiRPVtGlTPfroo5KkG264QS+++KLGjRtHYQMAAADwsYB/ePg5a9euvehrzZo1zn4AAAAArKF27dpauXKl833btm31+++/6+eff1aXLl2uyD4PHDig3bt3q23bti7tbdu2VWxsrFv/FStWqEWLFgoLC3O21atXT6VLl9by5cudfTxtb+PGjTpy5MgVOAoAAACg4LLEFRshISGqXLmyJOmrr75Senq6y/KIiAi1adPGpR8AAACAwHf//ffrtttuc2mrUqWKVq9erZSUlCuyz3NXeF94Zbjdbld8fLzH/nXq1HFrP79/QkKCx+1JZ5/PUa5cObf1MzIylJGR4Xyfmpoq6eyzRRwOxyWPw+FwyBiTq77IGXn0Hjn0Hjn0DfLoPXLoPXLoPXLoG1bMY15itURh43z9+vVTy5YtZYxxtv3yyy9q06aNSxsAAACAwFe8eHEVL17crT00NFRlypS5Ivs8d3uroCDXC9htNluO/S/se2F/T31y2t45MTExGjNmjFt7UlKS28lcnjgcDqWkpMgY4zE+5A559B459B459A3y6D1y6D1y6D1y6BtWzGNaWlqu+1qusBEeHq5Zs2a5tFWvXt1P0QAAAAC4HAcOHJB09vt9uXLlFBcXpwoVKig4OFiS9M477+j555/XyZMn1aZNG5fbVXkrOjpa0tmrLKpVq+ZsP3TokPMqiwv7e3qO3/n9PfU5dOiQJHncpiQNHz5cgwYNcr5PTU1VxYoVFRkZqYiIiEseh8PhkM1mU2RkpGUmq4GIPHqPHHqPHPoGefQeOfQeOfQeOfQNK+axUKFCue5rucLGpc56AgAAABD46tSpo5tvvlmZmZn67bff9Nprr2nEiBHq37+/vvzyS82bN0/PP/+8ihQpot9++82n+y5Xrpzq1q2r7777Tv3793e2L126VK1atXLr37JlS/Xq1UtZWVkKCTk7hdq+fbsSExPVvHlzZ5/vvvtOTz/9tMv26tat6/E2VNLZok54eLhbe1BQUK4nnzabLU/94Rl59B459B459A3y6D1y6D1y6D1y6BtWy2Ne4rTGEQEAAAC4qtSoUUM///yzTp8+rfvvv19bt26VJB08eFCSXG4zGxoa6vPbzg4ZMkTjx4/Xjh07JEkLFy5UbGys+vbt69a3devWKlu2rF5++WVlZ2crJSVF/fr1U48ePZy3y3ruuee0fPlyLVq0SJK0Y8cOvfrqqxo6dKhP4wYAAABgkSs2zp/EpKenq3fv3h6XAQAAALCGc1dih4eH69///rdeeOEFffPNNzp27Jg++eQTHT9+XJ9++qmqVKmiwoUL68yZMwoLC/PZ/jt16qTU1FS1adNGJ06cUHR0tBYvXqxq1aopLi5ODRs21OTJk9W+fXuFhIRoyZIleu6551SxYkUFBQWpffv2GjdunHN71apV0+LFizVo0CA9++yzKly4sF555RV17NjRZzEDAAAAOMsShY3zbz81efJktwfpPfDAA/kdEgAAAAAfsNlsatiwoSpXrqzNmzfr1KlT2rhxo06ePKmNGzfKZrMpNDRU2dnZPt93nz591KdPH7f26OhoxcXFubUtXLjwottr3Lixfv/9d5/GCAAAAMCdJQob5+vQoUOOy3j+BgAAAGAtmZmZatCggRITE/XLL79ow4YNmjBhglatWqXx48dLkl566SXL3BcYAAAAwJVnidlBXFycgoODL/k61y83UlJS9Mwzz6hSpUqqVKmS6tWrp6+++uoKHwkAAACA84WFhWnRokVq1aqVbDabSpcu7dYnKyvL40O2AQAAABRMlrhi4/Tp0z7fZvv27RUdHa2tW7eqaNGiWrFihdq2bSu73a7bb7/d5/sDAAAA8H+SkpI0a9YsJSUlKTY2VqVLl9aKFSv0r3/9S3v27FGPHj38HSIAAACAAGWJwsaVODvr448/VvHixZ0PILznnntUrVo1rVmzhsIGAAAAcIV17NhRO3fuVMeOHbVjxw5lZWVp7dq1OnTokP7++2+lpqbq0KFDeuihh1SpUiV/hwsAAAAggFiisHElREZGOv8/PT1dH374obZv365GjRr5MSoAAACgYIiJiZEkHTlyROXKlXNbfvDgQX3yySd66aWX9MILL+R3eAAAAAAC2FVT2Dh06JBeeuklzZ49O0/rRUdHKyEhQTfddJO+/PJL1a9f32O/jIwMZWRkON+npqZKkhwOhxwOR6725XA4ZIz5f+3deZhU5Zk34KeapREUEWhAGkSJgEZcEMhojCYCjiGCxi1xicRtQjRKADPgwgQZSMyIS74oRpMohkQzasZRLsUNwSGouGYmRiFuYQcBZYvQzdLn+8OhJ013Q0MVXXXo+74uLqlT76n3qadfqvv116eqzuN3+ng5eZQ6zLODeuu7hu172BB7kO38FRGR7M55e1EPcjF/oa3F+po/lzVYi9nPX9P3lYbWg1zUkOu1mMYeZFtDob0mFkIN+V6H9SWb+cvKyuK8886LFi1axAMPPBAREV26dKnyNrRJkkS7du3i9NNPz7pWAABg77HXBBvNmzePhx56aJeDjcWLF8fq1avjtttui3vvvTe+8pWvxL777ltt3E033RTjxo2rdnzlypVRVlZWp7kqKipi7dq1kSRJFBVl/7ntWxs1zfox6qLRihUFU8P2PWyIPch2/oqIWFfUOJKI2JVVuDf1IBfzF9parK/5c1mDtZj9/DV9X2loPchFDblei2nsQbY1FNprYkT+vw75Xof1Zf369bt97gUXXBDt2rWLu+66q/JY8+bNY8GCBVXGXX/99XHGGWfE1KlTK99GFgAAaNj2mmCjZcuWUV5eHkmSRCaT2aVzDzjggBg/fnx88YtfjDvvvDOuvfbaamOuu+66GDlyZOXtdevWRefOnaOkpCRatmxZp3kqKioik8lESUlJToKNLVs3Zf0YddG4XbuCqWH7HjbEHmQ7f0VEZCKi7dZNu/Q/TvamHuRi/kJbi/U1fy5rsBazn7+m7ysNrQe5qCHXazGNPci2hkJ7TYzI/9ch3+uwvjRr1my3z73tttvi4IMPrnLstNNOqzZuwoQJcfvtt0d5eblgAwAAiIgUBRuvv/569OnTJyZOnBgtWrSIK6+8MiIitm7dGkuWLIl33303DjjggDqFGhUVFTFt2rQYNGhQleNt27aN5cuX13hOcXFxjR9iXlRUtEshRSaT2eVzapP9I9Rxnh3Umo8a/r6HDbUH2c6f+d/zduXcva0HuZi/kNZifc2f6xqsxezn3/77SkPsQS5qyOVaTGsPsq2hkF4TI/L/dcj3Oqwv2cy/fagREXHLLbfUOMc111yz2/MAAAB7n/zuhOpo9erVMWDAgFi0aFE8++yzMW7cuLj99tsjIqJ3797Rs2fPuOKKK2Ls2LF1eryVK1fGZZddFuPGjav83IxnnnkmnnnmmRp/SwwAAMi9Cy64oPLvJ510Uh4rAQAA0iQVV2w88MADceqpp0bnzp2ja9euce6558Z9990XH3/8cWzevDnWrFmzS78t1r59+5gzZ05ce+210bVr10iSJNq3bx/3339/nHLKKXvwmQAAANvMmTOn8u/vvvtuTJs2rdqYTCYTPXr0iK5du9ZnaQAAQAFLRbDx5JNPxtVXXx0REV27do1ly5bF9OnTY9CgQfHuu+/Ge++9Fz169NilxzzkkEPioYce2hPlAgAAdZAkSeXf169fH7/61a+qHIuIKCsri9dffz1WrlxZ3+UBAAAFKhXBRlFRUfTv3z8iIjp27BhPPfVU7LvvvvHUU0/FmWeeGUOGDIlZs2bV+BkYAABAYXn11VcjImLTpk2xaNGi6Ny5c7Rr1y4effTRGse3bt26PssDAAAKXCo+Y+PJJ5+sDC3atWsXH374YURE7LPPPjF16tTYf//945xzzoktW7bks0wAAKAORo8eHaNHj47Vq1fHY489FhGfveVURMQ//uM/xhe+8IX4whe+EEOGDIlPP/3ULzABAABVpCLY+Hvdu3ePiy66qPJ206ZNY+rUqbF+/fo466yzYvPmzXmsDgAA2JmZM2fGzJkzo3379pVvObvN4sWL45FHHomHH344/vznP0eLFi1i6dKleaoUAAAoRKkLNg455JD43ve+V+VYs2bN4sknn4yjjz46GjVqlKfKAACAXbHtKo2/16hRo+jSpUscfPDBOxwHAAA0XKn4jI26aNGiRYwfPz7fZQAAADuxYcOGSJIkkiSJrVu3VvnlpA0bNsRtt90WSZLEypUrY/Xq1XHAAQfksVoAAKDQpO6KDQAAIN1KS0ujc+fOsWjRohgzZkxERCRJEhERw4cPj82bN8eWLVviqquuqjwOAACwTWqCjccffzwiIr7zne9ERMSYMWPio48+ymdJAADAbli9enV88skn0blz52jVqlV897vfjW9961vx7rvvxqmnnhpnnnlm5Z9Vq1bFu+++GxUVFfkuGwAAKBCpeSuqcePGxRlnnBGvvfZa/OUvf4nnnnsuLr300vjwww+rje3atWseKgQAAHZFJpOJ733ve/H73/8+fvazn8WPf/zjaNOmTXTo0KHKlRqZTCaef/75KCkpyWO1AABAoUhNsLHNunXrYujQofHLX/4yvvjFL8Y+++wTy5YtiwMPPDCWLl0apaWlNYYdAABAYUmSJPbdd9+4+OKL4+KLL44HH3wwbrjhhhgxYkRccskl+S4PAAAoUAX/VlTdunWL7t27x9y5c6N79+4xf/78OOGEE+Koo46KAw44IP76179Gt27d4q9//Wt07dpVqAEAAClxyy23VLl9wQUXxH/913/FO++8E59++mmeqgIAAApdwV+xMWfOnEiSJPr37x8zZsyI448/Pt5777347W9/G5lMJiKi8r8AAEB6nH322dWOHXTQQTFx4sQ8VAMAAKRFwQcbbdq0iYiIoqKiaNOmTbRo0SKmTJkSJ510kg8QBACAvdDcuXNj8+bNVY4dddRReaoGAAAoNAUfbGwzfvz4iIj49re/Hc2aNYtRo0bFD3/4wzxXBQAA7K7LL7+82i8r3XfffTFs2LBYtWpVRHwWcnz+85+PN998Mx8lAgAABSg1wcYPf/jDGDRoUEyZMiW6dOkSL730Ur5LAgAAsnDCCSdUBhv33ntvnHDCCRER8dxzz1WO6dy5s1ADAACoouCDjQcffDAiIlavXh0PPvhgbNiwIX7xi1/EPffcE08//XSeqwMAAHbXJZdcUvn3F154IQYOHFhtjM/TAwAAtlfwwcbLL78cEREbNmyIl19+OZYuXRplZWXxt7/9LbZs2RKvvvpqbNy4MV599dUoKyuL1157Lfr27ZvnqgEAAAAAgD2h4IONO+64IyIiXnzxxbjjjjti9uzZMWXKlLjggguiS5cucc0110SHDh3immuuiU6dOsU111wTs2bNynPVAADAjkybNq3K7aVLl8Yrr7wSZWVlVY5vfxsAAKDgg41tkiSp/PuRRx4Zp556anTv3j0uu+yyPFYFAADsjokTJ1a5PXfu3Pj444/j2WefrXJ8/fr19VkWAACQAqkJNt54442IiPjXf/3XiIj47ne/G8XFxfksCQAA2E0zZ86scvuiiy6KSy65JPr161fleOfOneuzLAAAIAVSE2wUFRVFRMTgwYMjIuKQQw7JZzkAAEA98OHhAADA9oryXcCueO6556J79+7xz//8zzXe/8wzz8Sll15az1UBAAC7Y+PGjbFhw4bYsGFDbN261edpAAAAdZKaKzYiIk455ZSYPXt29OrVKyZOnBg9e/aMyy+/PK6++upo1KhRTJ48OU444YR8lwkAANTBQQcdFFu3bo2Izz5T7+mnn45PPvkkbr311vj0008jImLt2rUxfvz4+Jd/+Zd8lgoAABSQVAQbX/ziFyv/vnnz5thnn30iIuKTTz6JDz/8MAYMGBDDhw+PP/3pT/HrX/86X2UCAAC7YOXKlbXelyRJRESMHDkyKioq6qskAAAgBVIRbCxcuDCef/75ytvt27ePiIhmzZrFz372s7jnnnvirLPOiunTp/tAcQAASLlrrrkm3yUAAAAFLBWfsdGkSZM45JBDokePHtGjR49o1apV5X2vvvpq3HHHHTF06NC4/fbb81ckAACQcwsWLMh3CQAAQIFJRbCRJEl069Yt2rZtG7169YopU6ZERMSyZcti5MiR8dvf/jbuuuuuaNGiRdx33315rhYAAKiLDRs2RL9+/eK5556L1atXx/XXX1/5tlO/+93vokePHnHFFVfkuUoAAKDQpOKtqObPn1/591deeSUuvfTS2HfffePQQw+N2bNnV943bty4+NrXvhYXX3xxFBWlIrMBAIAGacmSJfGNb3wjevXqFZ/73OeiT58+MWjQoKioqIiPP/44pk2bFtddd10sWrQoXnzxxTjhhBPyXTIAAFAgCv7//n/44Ydx5ZVXVt7eunVr/OEPf4izzjorfvnLX0ZExJ133hlLliyJ7t27xy233CLUAACAArZs2bLo379/XHnllXHnnXfGk08+GTfffHN86UtfikwmE7/4xS/iiiuuiCOPPDKaN28e5557br5LBgAACkjBJwBt2rSJhx9+uPKS9PPOOy9at24dERFDhgyJiIgVK1bEnDlzIiLi61//el7qBAAA6ubAAw+M1157LS688MIYO3ZsnHTSSXH22WfH8uXLo1+/fjFr1qw49NBDo3fv3nHNNdfEpk2b8l0yAABQQAo+2Nh///2jR48eMXfu3Ij47PM2tnf44YfHH//4x/ouDQAA2A2bNm2KsWPHxjvvvBMHH3xwXH311TFgwIBYvnx5jB07NoYOHRq9e/eOgw46KA488MDo2rVrvksGAAAKSCo+Y6NXr15x9dVXR6dOnWL16tUxZMiQSJIkli9fHkOGDInly5fH+++/HwsXLoyIqPxwcQAAoPA0atQojjrqqDj//PPjyCOPjCeeeCLefvvtmDJlSvTr1y8iIlatWhXdunWLPn36xH777ZfnigEAgEKSimBj5MiRlVdsfPOb36w8ft5550VExJo1a2L48OFV7gMAAApTo0aN4uKLL45vf/vb8eMf/ziGDh0av/vd7+L999+PJEli8+bNsXTp0nj22WfjxRdfjKlTp0bfvn3zXTYAAFAgUhFsdO3adYeXnydJEvvvv3/07NkzunTpUo+VAQAAu+t//ud/YtiwYbHffvvFo48+GnfeeWc0bdo0OnfuHFu2bIkNGzbEvHnzYv/99893qQAAQAEp+M/Y2Gbjxo3Rr1+/eO6552L16tVx/fXXV36g+L//+7/HNddcE1dccUWeqwQAAHamvLw8ysrKYsaMGdG7d+949NFH4+WXX44hQ4bEWWedFffcc0907NgxfvjDH0aLFi3yXS4AAFBgUhFsLFmyJAYMGBCf//zn43Of+1z06dMnPv3006ioqIiVK1fGtGnT4rrrrovjjz8+XnzxxXyXCwAA7MC0adPi8MMPj65du8bTTz8dP/nJT2Ljxo0xb9686N69e8yaNSsmTpwY559/fnTr1s2HhwMAAFUU/FtRLVu2LPr37x//8i//EhdeeGHccccdcfPNN0dFRUVkMpn4xS9+EVdccUUUFxfHxx9/HOeee24sXbo032UDAAC1OPPMM+Oggw6K66+/PoqKiqKkpCTWrl0b06dPj/nz58ebb74ZmUwmDjnkkGjVqlW+ywUAAApMwV+xceCBB8Zrr70WF154YYwdOzZOOumkOPvss2P58uXRr1+/mDVrVhx66KHRu3fvuOaaa2LTpk35LhkAANiJ3r17x7Bhw6K4uDgef/zx+M1vfhNJkkRxcXHMmjUrbrzxxujTp08cytFZ2QAAKVZJREFUd9xx+S4VAAAoMAV/xcamTZti7Nixcfnll8fBBx8cV199dTRt2jT+4R/+IcaOHRtr1qyJ3r17RyaTic2bN7tMHQAAUuK0006LU089NRo3/mxb8swzz0RExOjRo2P06NEREfHRRx9FcXFx3moEAAAKT8EHG40aNYqjjjoqzj///DjyyCPjiSeeiLfffjumTJkS/fr1i4iIVatWRbdu3aJPnz6x33775bliAACgrraFGhERRx99dLX727dvX5/lAAAAKVDwb0XVqFGjuPjii+O///u/4/DDD4+hQ4fG8ccfH1/84hcjSZLYtGlTLF26NCZNmhTdu3eP1157Ld8lAwAAAAAAe0jBX7Gxzf/8z//EsGHDYr/99otHH3007rzzzmjatGl07tw5tmzZEhs2bIh58+bF/vvvn+9SAQAAAACAPaTgr9goLy+PsrKymDFjRvTu3TseffTRePnll2PIkCFx1llnxT333BMdO3aMH/7wh9GiRYt8lwsAAAAAAOxBBX/FxrRp02LkyJFx++23x9NPPx3nnXdefOELX4h58+ZF9+7dIyJi1qxZleMzmUx8+OGH+SoXAAAAAADYgwo+2DjzzDPjoIMOiuuvvz6KioqipKQk1q5dG9OnT4/58+fHm2++GZlMJg455JBo1apVvssFAAAAAAD2oIIPNiIievfuHcOGDYtf/vKX8fvf/z4aN24cHTp0iOLi4pg1a1bMnDkz3n777Wjbtm3MmTMn3+UCAAAAAAB7SCqCjYiI0047LU499dRo3Pizkp955pmIiBg9enSMHj06IiI++uijKC4uzluNAAAAAADAnlXwHx7+97aFGhERRx99dLX727dvX5/lAAAAAAAA9SxVwQYAAAAAANCwCTYAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIDcEGAAAAAACQGoINAAAAAAAgNQQbAAAAAABAagg2AAAAAACA1BBsAAAAAAAAqSHYAAAAAAAAUkOwAQAAAAAApIZgAwAAAAAASA3BBgAAAAAAkBqCDQAAAAAAIDUEGwAAAAAAQGoINgAAAAAAgNQQbAAAAAAAAKkh2AAAAAAAAFJDsAEAAAAAAKSGYAMAAAAAAEgNwQYAAAAAAJAagg0AAAAAACA1GnSwcd9990XPnj2jtLQ0DjvssPj5z3+e75IAAIA9bO7cuTFw4MDo0qVLdOnSJX70ox9FkiS1jk+SJCZOnBg9evSI0tLS+MpXvhLvvPNOlTFr1qyJ73znO3HQQQdFaWlp9O/fP/70pz/t6acCAAANUoMNNn7zm9/E2LFj46GHHoolS5bEY489FuPHj48HHngg36UBAAB7yKpVq+Lkk0+OU089NebPnx8vvfRSPPDAAzFx4sRaz5kwYUJMnjw5ZsyYEYsXL44zzjgjBgwYEGvWrKkcc/bZZ8fGjRtj7ty5sWTJkjjjjDPilFNOidWrV9fDswIAgIalwQYbc+bMiZtvvjmOOOKIiIg47LDD4vzzz4//+I//yHNlAADAnnL33XdHSUlJDB8+PDKZTJSWlsb48ePj1ltvjc2bN1cbv3Hjxpg4cWKMHz8+SktLI5PJxIgRI6Jt27YxZcqUiIj46KOPYu3atXHPPfdEixYtIiJi2LBhsWXLlvjDH/5Qr88PAAAaggYbbEyaNCnOP//8KsfeeuutaNmyZZ4qAgAA9rQZM2bE4MGDqxwbNGhQrFy5Mt58881q41977bX429/+Fqeddlq1c5566qmIiGjfvn28/vrr0bx588r7Fy5cGGvXrrW/AACAPaBxvgsoBJs3b46RI0fGyy+/HC+//HKNY8rLy6O8vLzy9rp16yIioqKiIioqKuo0T0VFRSRJUufxO328nDxKHebZQb31XcP2PWyIPch2/oqISHbnvL2oB7mYv9DWYn3Nn8sarMXs56/p+0pD60Euasj1WkxjD7KtodBeEwuhhnyvw/qS7/l3x9KlS6Njx45VjhUXF0ebNm1iyZIlNY5v3bp1NGvWrMrx0tLSeOKJJ2qc44MPPojTTz89TjrppPjyl79cay3Z7jFyvb9oqPQxe3qYPT3MDX3Mnh5mTw+zp4e5kcY+7kqtDT7YWLBgQXzzm9+MdevWxezZs6Nnz541jrvpppti3Lhx1Y6vXLkyysrK6jRXRUVFrF27NpIkiaKi7C+W2dqoadaPUReNVqwomBq272FD7EG281dExLqixpHErl2ytTf1IBfzF9parK/5c1mDtZj9/DV9X2loPchFDblei2nsQbY1FNprYkT+vw75Xof1Zf369Xmdf3srV66MXr161Xr/97///WjSpEmNP4tnMpkaz9nV8Q8//HD80z/9U5x77rlxxx131DouIvs9Rq73Fw2VPmZPD7Onh7mhj9nTw+zpYfb0MDfS2Mdd2V806GDjjTfeiIEDB8aQIUPiRz/6URQXF9c69rrrrouRI0dW3l63bl107tw5SkpK6nx5eUVFRWQymSgpKcnJYtqydVPWj1EXjdu1K5gatu9hQ+xBtvNXREQmItpu3bRL/+Nkb+pBLuYvtLVYX/PnsgZrMfv5a/q+0tB6kIsacr0W09iDbGsotNfEiPx/HfK9DuvL9lcx5FtJSUksXrx4h2NmzJgRS5curXKsvLw8Vq9eHaWlpdXGd+rUKVatWhWbNm2Kpk3/L7BatmxZtfE33nhj3HPPPfG73/0uvva1r+203mz3GLneXzRU+pg9PcyeHuaGPmZPD7Onh9nTw9xIYx93ZX/RYIONBQsWxNe+9rWYNGlSnHvuuTsdX1xcXGPwUVRUtEsLI5PJ7PI5tamv5bijWvNRw9/3sKH2INv5M/973q6cu7f1IBfzF9JarM9vT9Zi/r8Otb0m5quGKsfzPP/u1pDLtZjWHmRbQyG9Jkbk/+uQ73VYX/I9/+746le/Gr/+9a9jwoQJlcdmzpwZrVu3jmOPPbba+GOPPTZKSkri6aefjtNPP73y+PTp06t8Zt8dd9wRDz30ULzxxhvV3uqqNrnYY+Ryf9GQ6WP29DB7epgb+pg9PcyeHmZPD3MjbX3clTrT8Yz2gCuuuCKuvPLKOoUaAADA3mHIkCGxbNmyuOuuuyLisysvRo0aFSNGjIgmTZpUG9+kSZMYPnx4jBkzJpYvXx4REZMmTYr58+fHRRddFBGffVD4DTfcEFOnTq1zqAEAAOy+BnvFxlNPPRVvvPFG/PKXv6x2384uXwcAANLpgAMOiOnTp8dVV10VEyZMiKZNm8bll18eo0aNqhyz7ZefHnnkkYiIGD16dGzdujWOO+642LRpU/To0SOmT58eBxxwQER8dsVHWVlZnHzyydXm+8Y3vhG33XZbPTwzAABoOBpssJEkSb5LAAAA8uCII46ImTNn1nr/tkBjm6KiohgzZkyMGTOmxvHf/va349vf/nZOawQAAGrXYN+KCgAAAAAASB/BBgAAAAAAkBqCDQAAAAAAIDUEGwAAAAAAQGoINgAAAAAAgNQQbAAAAAAAAKkh2AAAAAAAAFJDsAEAAAAAAKSGYAMAAAAAAEgNwQYAAAAAAJAagg0AAAAAACA1BBsAAAAAAEBqCDYAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIDcEGAAAAAACQGoINAAAAAAAgNQQbAAAAAABAagg2AAAAAACA1BBsAAAAAAAAqSHYAAAAAAAAUkOwAQAAAAAApIZgAwAAAAAASA3BBgAAAAAAkBqCDQAAAAAAIDUEGwAAAAAAQGoINgAAAAAAgNQQbAAAAAAAAKkh2AAAAAAAAFJDsAEAAAAAAKSGYAMAAAAAAEgNwQYAAAAAAJAagg0AAAAAACA1BBsAAAAAAEBqCDYAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIDcEGAAAAAACQGoINAAAAAAAgNQQbAAAAAABAagg2AAAAAACA1BBsAAAAAAAAqSHYAAAAAAAAUkOwAQAAAAAApIZgAwAAAAAASA3BBgAAAAAAkBqCDQAAAAAAIDUEGwAAAAAAQGoINgAAAAAAgNQQbAAAAAAAAKkh2AAAAAAAAFJDsAEAAAAAAKSGYAMAAAAAAEgNwQYAAAAAAJAagg0AAAAAACA1BBsAAAAAAEBqCDYAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIDcEGAAAAAACQGoINAAAAAAAgNQQbAAAAAABAagg2AAAAAACA1BBsAAAAAAAAqSHYAAAAAAAAUqPBBhsVFRUxZ86cGDlyZLRu3Truv//+fJcEAAAAAADsRIMNNiZPnhzDhg2L5s2bR6NGjfJdDgAAAAAAUAcNNti47LLL4tVXX40JEyZEixYt8l0OAAAAAABQBw022AAAAAAAANKncb4LSIvy8vIoLy+vvL1u3bqI+OyzOioqKur0GBUVFZEkSZ3H7/TxcvIodZhnB/XWdw3b97Ah9iDb+SsiItmd8/aiHuRi/kJbi/U1fy5rsBazn7+m7ysNrQe5qCHXazGNPci2hkJ7TSyEGvK9DutLvucHAAAaJsFGHd10000xbty4asdXrlwZZWVldXqMioqKWLt2bSRJEkVF2V8ss7VR06wfoy4arVhRMDVs38OG2INs56+IiHVFjSOJXbtka2/qQS7mL7S1WF/z57IGazH7+Wv6vtLQepCLGnK9FtPYg2xrKLTXxIj8fx3yvQ7ry/r16/M6PwAA0DAJNurouuuui5EjR1beXrduXXTu3DlKSkqiZcuWdXqMioqKyGQyUVJSkpNgY8vWTVk/Rl00bteuYGrYvocNsQfZzl8REZmIaLt10y79j5O9qQe5mL/Q1mJ9zZ/LGqzF7Oev6ftKQ+tBLmrI9VpMYw+yraHQXhMj8v91yPc6rC/NmjXL6/wAAEDDJNioo+Li4iguLq52vKioaJdCikwms8vn1Ka+PiBlR7Xmo4a/72FD7UG282f+97xdOXdv60Eu5i+ktVifH5hkLeb/61Dba2K+aqhyPM/z724NuVyLae1BtjUU0mtiRP6/Dvleh/Ul3/MDAAANk50IAAAAAACQGoINAAAAAAAgNbwVVUTMnz8/3yUAAAAAAAB14IoNAACgQZk7d24MHDgwunTpEl26dIkf/ehHkSRJreOTJImJEydGjx49orS0NL7yla/EO++8U+v4n/70p5HJZOKFF17YA9UDAACCDQAAoMFYtWpVnHzyyXHqqafG/Pnz46WXXooHHnggJk6cWOs5EyZMiMmTJ8eMGTNi8eLFccYZZ8SAAQNizZo11ca+9dZb8bOf/Szatm27B58FAAA0bIINAACgwbj77rujpKQkhg8fHplMJkpLS2P8+PFx6623xubNm6uN37hxY0ycODHGjx8fpaWlkclkYsSIEdG2bduYMmVKlbFlZWVxwQUXxM033xwtWrSor6cEAAANjmADAABoMGbMmBGDBw+ucmzQoEGxcuXKePPNN6uNf+211+Jvf/tbnHbaadXOeeqpp6oc++d//ufo1q1bnHPOObkvHAAAqOTDwwEAgAZj6dKl0bFjxyrHiouLo02bNrFkyZIax7du3TqaNWtW5XhpaWk88cQTlbenTZsW//Ef/xF/+tOf6lxLeXl5lJeXV95et25dRERUVFRERUXFTs+vqKiIJEnqNJba6WP29DB7epgb+pg9PcyeHmZPD3MjjX3clVoFGwAAwF5h5cqV0atXr1rv//73vx9NmjSJoqLqF65nMpkaz6nL+BUrVsSll14akydP3qXP1rjpppti3Lhx1Y6vXLkyysrKdnp+RUVFrF27NpIkqbFG6kYfs6eH2dPD3NDH7Olh9vQwe3qYG2ns4/r16+s8VrABAADsFUpKSmLx4sU7HDNjxoxYunRplWPl5eWxevXqKC0trTa+U6dOsWrVqti0aVM0bdq08viyZcsqx19yySVx9tlnx8CBA3ep3uuuuy5GjhxZeXvdunXRuXPnKCkpiZYtW+70/IqKishkMlFSUpKazWoh0sfs6WH29DA39DF7epg9PcyeHuZGGvu4/VXSOyLYAAAAGoyvfvWr8etf/zomTJhQeWzmzJnRunXrOPbYY6uNP/bYY6OkpCSefvrpOP300yuPT58+Pc4///yI+OxtqCIi7rrrrirnnnzyyVFaWlpr2FJcXBzFxcXVjhcVFdV585nJZHZpPDXTx+zpYfb0MDf0MXt6mD09zJ4e5kba+rgrdabjGQEAAOTAkCFDYtmyZZUhxLJly2LUqFExYsSIaNKkSbXxTZo0ieHDh8eYMWNi+fLlERExadKkmD9/flx00UUREZEkSbU/Xbp0iZkzZ+70ChIAAGDXuWIDAABoMA444ICYPn16XHXVVTFhwoRo2rRpXH755TFq1KjKMeeee25ERDzyyCMRETF69OjYunVrHHfccbFp06bo0aNHTJ8+PQ444IC8PAcAAGjoBBsAAECDcsQRR8TMmTNrvX9boLFNUVFRjBkzJsaMGVPnOebPn7+75QEAADvhragAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIDcEGAAAAAACQGoINAAAAAAAgNQQbAAAAAABAagg2AAAAAACA1BBsAAAAAAAAqSHYAAAAAAAAUkOwAQAAAAAApIZgAwAAAAAASA3BBgAAAAAAkBqCDQAAAAAAIDUEGwAAAAAAQGoINgAAAAAAgNQQbAAAAAAAAKkh2AAAAAAAAFJDsAEAAAAAAKSGYAMAAAAAAEgNwQYAAAAAAJAagg0AAAAAACA1BBsAAAAAAEBqCDYAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIDcEGAAAAAACQGoINAAAAAAAgNQQbAAAAAABAagg2AAAAAACA1BBsAAAAAAAAqSHYAAAAAAAAUkOwAQAAAAAApIZgAwAAAAAASA3BBgAAAAAAkBqCDQAAAAAAIDUEGwAAAAAAQGoINgAAAAAAgNQQbAAAAAAAAKkh2AAAAAAAAFJDsAEAAAAAAKSGYAMAAAAAAEgNwQYAAAAAAJAagg0AAAAAACA1BBsAAAAAAEBqCDYAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIDcEGAAAAAACQGoINAAAAAAAgNQQbAAAAAABAagg2AAAAAACA1BBsAAAAAAAAqSHYAAAAAAAAUqNBBxv3339/9OzZMzp16hR9+/aN2bNn57skAAAAAABgBxpssPGb3/wmrrvuunjkkUdi8eLFMWrUqDjttNPiww8/zHdpAAAAAABALRpssDFu3Lj4wQ9+EIcffnhERJx77rlx0kknxZ133pnnygAAAAAAgNo0yGBj4cKF8cEHH8TgwYOrHB88eHA89dRTeaoKAAAAAADYmcb5LiAfli5dGhERHTt2rHK8tLQ0lixZUuM55eXlUV5eXnl77dq1ERGxZs2aqKioqNO8FRUVsW7dumjatGkUFWWfKW3ZsDHrx6iLxmvWFEwN2/ewIfYg2/krImJdo63RZOumXUo296Ye5GL+QluL9TV/LmuwFrOfv6bvKw2tB7moIddrMY09yLaGQntNjMj/1yHf67C+rFu3LiIikiTJax17i2193NbXnamoqIj169dHs2bNcrK/aKj0MXt6mD09zA19zJ4eZk8Ps6eHuZHGPu7K/iKTNMBdyBtvvBF9+vSJTz/9NJo3b155fNq0aXHeeefVuJG48cYbY9y4cfVZJgAApMKiRYuiU6dO+S4j9RYvXhydO3fOdxkAAJBXddlfNMhg46OPPooOHTrEe++9F4ceemjl8XvvvTduueWWmDt3brVztr9io6KiIj755JNo06ZNZDKZOs27bt266Ny5cyxatChatmyZ/RNpgPQwe3qYG/qYPT3Mnh7mhj5mTw+zl9YeJkkS69evj44dO6bmt8AKWUVFRSxdujT222+/Ou0x0rpuCo0+Zk8Ps6eHuaGP2dPD7Olh9vQwN9LYx13ZXzTIt6Jq3759HHPMMTFt2rQYNmxY5fHnnnsuBg4cWOM5xcXFUVxcXOVYq1atdmv+li1bpmYxFSo9zJ4e5oY+Zk8Ps6eHuaGP2dPD7KWxh/vvv3++S9hrFBUV7daVL2lcN4VIH7Onh9nTw9zQx+zpYfb0MHt6mBtp62Nd9xcN9teqRo0aFTfffHO8++67ERHx+OOPx1NPPRVXXnllnisDAAAAAABq0yCv2IiIOP/882PdunUxaNCg+Nvf/hadOnWKJ554ospbUwEAAAAAAIWlwQYbERFDhw6NoUOH1tt8xcXFMXbs2GpvaUXd6WH29DA39DF7epg9PcwNfcyeHmZPD9kd1k1u6GP29DB7epgb+pg9PcyeHmZPD3Njb+9jg/zwcAAAAAAAIJ0a7GdsAAAAAAAA6SPYAAAAAAAAUkOwAQAAAAAApIZgI8fuv//+6NmzZ3Tq1Cn69u0bs2fPrnXskiVL4pvf/GYcfPDBUVpaGiNGjIjy8vJ6rLbw3HfffdGzZ88oLS2Nww47LH7+85/vcPzgwYOjTZs20alTp8o/J554Yj1VW7jeeOONaNKkSZW+dOrUKf7zP/+zxvHWYlWLFy+u1rtOnTrFPvvsEwMHDqzxHGsxoqKiIubMmRMjR46M1q1bx/3331/l/vLy8rj22mvj0EMPjY4dO8bpp58eS5Ys2eFjzpkzJ0488cQ46KCDolu3bvGLX/xiDz6D/NtZDzdt2hSjRo2q/Lf6D//wD/Ff//VfO3zMli1bRseOHauszVGjRu3BZ5FfO+vhrbfeGvvuu2+1f9/Lly+v9TGtw/sr73vkkUdqfH0sKiqKf/u3f6v1MRvaOozY+c80XhOpK/uL7NljZM/+Ijv2F7vH/iJ79he5YY+RPXuM3LDH2E5CzkyZMiXp0KFD8s477yRJkiQPP/xw0rJly+SDDz6oNra8vDw5/PDDk5EjRyabN29OVq9enZx44onJFVdcUd9lF4wpU6YknTp1Sv785z8nSZIkc+fOTQ488MDkt7/9ba3nHHPMMckzzzxTXyWmxmOPPZYcf/zxdRprLdbNmjVrktatWyfPPfdcjfdbi0nyq1/9Kunbt29yww03JG3btk0mT55c5f5LL700Oemkk5LVq1cnmzdvTkaMGJEceeSRyZYtW2p8vLlz5yb77bdf8vvf/z5JkiR5++23k/bt2ycPPfTQnn4qeVOXHp5yyinJqlWrkiRJkkcffTRp3rx58v7779f4eGvWrEkymUxSVla2p0svGDvr4fDhw5Prrruuzo9nHVbv4fZmzZqVtGrVKlm5cmWN9zfEdViXn2m8JlIX9hfZs8fIDfuL3LO/2Dn7i+zZX+SGPUb27DGyZ49RnWAjhz73uc8lt9xyS5VjgwYNSkaMGFFt7G9+85ukdevWSXl5eeWx119/PWnatGmt/2j3dldeeWXy4IMPVjk2cuTI5Mwzz6z1nLZt21Zu9Pg/d955Z/KNb3yjTmOtxboZPXp0Mnjw4Frvtxar6tKlS5UfVBYsWJAUFRUlr7/+euWx8vLypE2bNsnjjz9e42NcdtllyaBBg6ocu+WWW5JevXrtkZoLzfY9LC8vT77whS8kCxYsqDLu2GOPTf7f//t/NT7Gn//856Rdu3Z7ssyCtn0PkyRJzjnnnOSuu+6q82NYh9V7uL3jjjsuufXWW2u9vyGuw539TOM1kbqyv8iePUZu2F/knv3FrrG/yJ79RW7YY2TPHmP32GNU562ocmThwoXxwQcfxODBg6scHzx4cDz11FPVxs+YMSNOPfXUaNq0aeWx3r17R5s2beL555/f4/UWokmTJsX5559f5dhbb70VLVu2rHF8eXl5rFq1Kjp16lQf5aXKtkud68Ja3Llly5bFHXfcERMmTKjxfmtx51544YVo37599O7du/JY06ZN4x//8R9rfI2M+Gxt1vSa+sc//jE++uijPVpvIWratGm88sorcdBBB1UeW79+fcyfP7/W18ldeS1oKHa1J9bhjj322GOxcOHC+N73vlfrmIa4Dnf2M43XROrC/iI37DFyw/4it+wvsud7afbsL3LHHiO37DFqZo9RnWAjR5YuXRoRER07dqxyvLS0tMb3Mlu6dGm1sTsa39Bs3rw5rr766nj55ZfjBz/4QY1jFi9eHM2bN4+77747evXqFV27do0LL7wwFi5cWM/VFp7FixfHJ598El//+teja9eu0bdv37jvvvtqHGst7tztt98eJ598chx11FE13m8t7tzurLOaziktLY2IsDYjYsWKFXHaaadFhw4d4pvf/GaNYxYvXhzFxcXxve99Lw477LD4/Oc/H9ddd11s2LChnqstHIsXL4433ngjvvSlL8UhhxwSAwYMiBdffLHW8dbhjv34xz+OESNGRHFxca1jGvo6rOlnGq+J1IX9Re7ZY+w++4vcsr/Inu+luWd/sfvsMXLLHmPn7DE+I9jIkSZNmkRERFFR1ZZmMplax28/dkfjG5IFCxbEiSeeGM8//3zMnj07evbsWeO4tWvXRtu2baNjx47x0ksvxVtvvRVt27aNfv36xaefflrPVReWTCYTK1asiJ/+9KfxwQcfxF133RVjxoyJe+65p9pYa3HH1qxZE3fffXetm98Ia7Eudmed1XSOdfmZmTNnxjHHHBOtWrWKWbNmxT777FPjuPLy8li/fn1861vfirfffjueffbZePnll+Oyyy6r54oLQ5IkUVxcHGVlZTF16tR4//3345JLLolTTjkl/vSnP9V4jnVYuxkzZsTcuXNj6NChOxzXkNdhbT/TeE2kLuwvcsseIzv2F7ljf5Ebvpfmlv3F7rPHyC17jJ2zx/g/go0c2Xb507bfrNpm2bJllUnX9uO3H7uj8Q3FG2+8EX379o0vfelL8cc//jGOPvroWscee+yxsWDBgrjwwgtjn332iRYtWsRtt90Wy5cvjz/84Q/1WHXhmTJlSjz55JNx8MEHRyaTib59+8b3v//9mDx5crWx1uKO/fa3v422bdvGl7/85VrHWIs7tzvrrKZzli1bFhHRoNfmr371qzj77LPjxz/+cUydOjXatGlT69grr7wy3nrrrTj++OOjUaNG0alTp/i3f/u3ePjhhxvkpjiTycT7778fP/nJT6J169bRqFGjuPDCC+PLX/5yPPjggzWeYx3W7q677opzzjkn9ttvvx2Oa6jrcEc/03hNpC7sL3LHHiN79he5Y3+RG76X5o79RXbsMXLLHmPH7DGqEmzkSPv27eOYY46JadOmVTn+3HPPxcCBA6uN/+pXvxrPPvtsbNmypfLYvHnzYsWKFdG/f/89Xm8hWrBgQXzta1+LSZMmxS233LLDS862qaioqHI7SZKoqKhIVbq4J2zfl4iIrVu31tgXa3HH7r333rjooot2uqasxR3r169frFixospvrGzdujVmzpxZ42tkxGdrs6bX1GOOOSbat2+/R+stVI8//niMHTs2Zs+eHRdffHGdztl+bW7dujUi0vVbGLm0K6+PEdZhbVauXBlTp06NIUOG1Gl8Q1uHO/uZxmsidWF/kRv2GLlhf5E79he54Xtpbthf5IY9Rm7YY+yYPUYN8vnJ5XubBx98MCktLU3+8pe/JEmSJI899ljSsmXL5L333qs2dvPmzckRRxyRXHvttcmWLVuSNWvWJP3790+GDh1a32UXjIEDByY33nhjncfPmjUr6datW/Lqq68mSZIkGzduTK688sqkW7duSVlZ2Z4qMxW++tWvJj/4wQ+STz/9NEmSJHnttdeSkpKS5N5776021lqs3bx585KISF555ZUdjrMWq+vSpUsyefLkKse+853vJP3790/Wrl2bbNmyJRk9enRyxBFHJJs3b67xMd57772kZcuWyeOPP54kSZL85S9/SUpLS5Pf/e53e7r8grB9D9evX5+0a9cueeGFF+r8GD/5yU+SU045JVmyZEmSJEmydOnS5IQTTkguuuiiXJdbkLbv4ccff5wccsghyYMPPphs3bo1qaioSO6///6kWbNmyTvvvFPjY1iH1f8tJ0mS3H333cl+++1X67/fv9cQ12Fdfqbxmkhd2F9kzx4jN+wvcsP+YvfZX2TP/iI37DGyZ4+xe+wxqhNs5Njdd9+ddOvWLTnwwAOTvn37JrNmzUqSJEkWLVqUlJaWJg8//HDl2EWLFiWnn356cuCBByalpaXJ8OHDG+wPKUmSJBGRtGvXLiktLa32J0lq7uHkyZOTXr16JR07dkzatGmTfP3rX0/++te/5ukZFI5FixYlF110UdKpU6ekXbt2Sbdu3ZJJkyZV3mct1s2tt96atGrVKtmyZUuV49biztX0g0pZWVkyfPjwpLS0NOnQoUNy+umnJ4sWLaq8/+GHH05KS0urHJs1a1bSp0+fpGPHjsmhhx6a3HPPPfX1FPJu+x6+8MILSSaTqfE18pxzzkmSpHoPN27cmNxwww3J5z73ueTAAw9MOnbsmAwbNizZsGFDPp5SvatpHb700kvJgAEDKv+tHnfcccmMGTMq77cOq6pt0zF48OBk8ODBNZ5jHe78Z5ok8ZpI3dlfZMceIzfsL3LD/mL32V9kz/4iN+wxsmePsXvsMarLJEmS5POKEQAAAAAAgLryGRsAAAAAAEBqCDYAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIDcEGAAXluOOOi/nz50dExLXXXhv3339/XusBAADSzR4DYO8j2AAgFRo3bhyHHXZYHHbYYdGhQ4e48cYbIyLisMMOi89//vNxzDHHxDHHHBP77rtvzJs3L77yla/E4YcfXnm8bdu2NjAAAEAlewyA9Gqc7wIAICJi69atccQRR8SCBQuif//+0aRJk1i5cmU0bdo0ysrKolWrVjFv3ryIiLj77rtj+fLllecOHz482rVrFxERY8aMqTx+1VVXRWlpaeU5AABAw2GPAbD3csUGAAWhUaNGMW/evDj66KPj+eefj3nz5sU//dM/xU033RTf/e53d3hu48aNK/9kMpkqj7nteFGRb3kAANCQ2GMA7L1csQFAwTnvvPOiWbNm8eGHH8a//uu/RkTE2rVro0+fPhERsXLlyrjkkksqx//0pz+Npk2bRkRUvnduRMSdd94ZzZo1i4iIhQsXxje+8Y16egYAAEAhsccA2LsINgAoOP/+7/8eBx98cFx77bWVx/bff/94/fXXI6L6ZeJTp06Ngw8+uMpjvPDCC1Vu7+w3sgAAgL2XPQbA3kWwAUDBOf3006Np06axZMmSuOmmm+p83rHHHhubNm2qvL1ly5b47ne/G8OHD98DVQIAAGlhjwGwdxFsAFBwtv121N//NtXatWvjuOOOi4iIFStWxJAhQ6qdt3Dhwli1alXl7e1/6woAAGiY7DEA9i6CDQAK3oYNG6J58+bxrW99KyIiZs+eXevYbe+RG1H9fXIBAAAi7DEA0k6wAUBB+MMf/lC5QRgwYECV+55++uno0KFDXHXVVRER0bhx41p/S2rbe+RG+G0qAABoyOwxAPZegg0ACsKJJ54Y77//fjzyyCOxYsWKuOSSS2LQoEFx3333xZ///OdYs2ZNnR7nmGOOqfz7xx9/HJdddtmeKRgAACho9hgAey/BBgAF4+23346rr746nn766WjevHlceumlccEFF8Rhhx0WJ598cmzevDmaNGkSW7ZsiUwmU+Nj/Pd//3eV28uXL49Vq1bFJ598Uus5AADA3skeA2DvVJTvAgBgm+985ztxyy23VP5G1Le+9a34+c9/Hk888URcdNFFMWnSpGjdunXccMMN0atXr2rnL1iwoNqx//zP/4xjjz02XnnllTjqqKP29FMAAAAKiD0GwN4pkyRJku8iACAioqysLJo1a1bt+PLly6NDhw55qAgAAEgzewyAvZNgAwAAAAAASA1vRQUAAAAAAKSGYAMAAAAAAEgNwQYAAAAAAJAagg0AAAAAACA1BBsAAAAAAEBqCDYAAAAAAIDUEGwAAAAAAACpIdgAAAAAAABSQ7ABAAAAAACkhmADAAAAAABIjf8PPQLamWEJpYkAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["時間制約違反: 0 期間\n"]}], "source": ["import pandas as pd\n", "import pulp\n", "import csv\n", "import random\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 130\n", "出荷遅れコスト単価 = 500\n", "\n", "# 稼働時間（分）\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "期間 = 20\n", "\n", "# CSVファイルを読み込む関数\n", "def read_csv(file_path):\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            品番リスト.append(row[header.index(\"part_number\")])\n", "            出荷数リスト.append(int(row[header.index(\"shipment\")]))\n", "            収容数リスト.append(int(row[header.index(\"capacity\")]))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"cycle_time\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            込め数リスト.append(int(row[header.index(\"cabity\")]))\n", "    \n", "    # read_csvからは初期在庫量を返さないように変更\n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "# 修正：初期在庫量を引数として受け取るように変更\n", "def solve_mip(initial_inventory_list_arg):\n", "    \"\"\"PuLPを用いてMIPを解く関数\"\"\"\n", "    \n", "    # 修正：ここではread_csvを呼び出さない\n", "    品番数 = len(品番リスト)\n", "    \n", "    # モデルの定義\n", "    model = pulp.LpProblem(\"ProductionScheduling\", pulp.LpMinimize)\n", "    \n", "    # インデックスの定義\n", "    品目 = range(品番数)\n", "    期間_index = range(期間)\n", "\n", "    # 決定変数\n", "    Production = pulp.LpVariable.dicts(\"Production\", (品目, 期間_index), lowBound=0, cat='Integer')\n", "    IsProduced = pulp.LpVariable.dicts(\"IsProduced\", (品目, 期間_index), cat='Binary')\n", "    Inventory = pulp.LpVariable.dicts(\"Inventory\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    Shortage = pulp.LpVariable.dicts(\"Shortage\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    WorkTime = pulp.LpVariable.dicts(\"WorkTime\", 期間_index, lowBound=0, cat='Continuous')\n", "    Overtime = pulp.LpVariable.dicts(\"Overtime\", 期間_index, lowBound=0, cat='Continuous')\n", "\n", "    # 目的関数\n", "    total_cost = pulp.lpSum(\n", "        在庫コスト単価 * Inventory[i][t] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        残業コスト単価 * Overtime[t] for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index\n", "    )\n", "    \n", "    model += total_cost, \"Total Cost\"\n", "\n", "    # 制約条件\n", "    bigM = 1000000\n", "\n", "    for i in 品目:\n", "        for t in 期間_index:\n", "            if t == 0:\n", "                # 修正：引数で受け取った初期在庫リストを使用\n", "                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]\n", "            else:\n", "                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]\n", "            \n", "            model += Production[i][t] <= bigM * IsProduced[i][t]\n", "\n", "    for t in 期間_index:\n", "        model += WorkTime[t] == pulp.lpSum(\n", "            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]\n", "            for i in 品目\n", "        )\n", "        \n", "        model += WorkTime[t] <= 定時 + Overtime[t]\n", "        model += WorkTime[t] <= 定時 + 最大残業時間\n", "        model += Overtime[t] >= WorkTime[t] - 定時\n", "        model += Overtime[t] >= 0\n", "\n", "    # Solverの設定\n", "    solver = pulp.GUROBI(msg=True)\n", "    \n", "    # 最適化の実行\n", "    model.solve(solver)\n", "    \n", "    print(\"ステータス:\", pulp.LpStatus[model.status])\n", "    if pulp.LpStatus[model.status] == 'Optimal':\n", "        print(\"総コスト:\", pulp.value(model.objective))\n", "\n", "        production_schedule = [[0] * 期間 for _ in range(品番数)]\n", "        for i in 品目:\n", "            for t in 期間_index:\n", "                production_schedule[i][t] = pulp.value(Production[i][t])\n", "\n", "        return production_schedule, pulp.value(model.objective)\n", "    \n", "    return None, None\n", "\n", "def plot_results(best_individual, initial_inventory):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 期間\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "        \n", "        # 今回の期間の在庫と生産量、遅れを計算\n", "        temp_inventory = inventory[:]\n", "        for i in range(len(品番リスト)):\n", "            production = best_individual[i][t]\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = production * (サイクルタイムリスト[i] / 込め数リスト[i])\n", "            daily_production_time += production_time + setup_time\n", "            temp_inventory[i] += production - 出荷数リスト[i]\n", "\n", "        for i in range(len(品番リスト)):\n", "            inventory[i] = temp_inventory[i]\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "            daily_inventory += inventory[i]\n", "            \n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].legend()\n", "\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "\n", "def simulate_production_schedule(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（MIP用の簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(initial_inventory_list, h_cost, c_cost, num_simulations=50, max_iterations=5):\n", "    \"\"\"初期在庫を更新する関数（optimize_initial_inventoryと一致させた版）\"\"\"\n", "    \n", "    品番数 = len(initial_inventory_list)\n", "    s = copy.deepcopy(initial_inventory_list)\n", "    \n", "    # h / (h+c) - optimize_initial_inventoryと同じ計算方法\n", "    prob_target = h_cost / (h_cost + c_cost)\n", "    \n", "    print(f\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration + 1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック\n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック - optimize_initial_inventoryと同じ条件\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    \n", "    # 修正：read_csvから受け取る変数から初期在庫量リストを削除\n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト = read_csv('data.csv')\n", "    \n", "    # 添付文書のアルゴリズムを適用するためのパラメータ\n", "    # These are illustrative values, you'd need to set them based on your problem\n", "    h_cost = 180  # Example unit holding cost\n", "    c_cost = 5000 # Example unit shortage cost\n", "    \n", "    # 新しい初期在庫リストを生成 (ここでは一旦ランダムな値を使用)\n", "    initial_inventory_list = [random.randint(shipment * 3, shipment * 5) for shipment in 出荷数リスト]\n", "\n", "    print(\"--- 元の初期在庫 ---\")\n", "    for i, stock in enumerate(initial_inventory_list):\n", "        print(f\"製品 {品番リスト[i]}: {stock}\")\n", "        \n", "    # 添付文書のアルゴリズムを適用して初期在庫を調整\n", "    adjusted_initial_inventory = adjust_initial_inventory(initial_inventory_list, h_cost, c_cost, num_simulations=50, max_iterations=5)\n", "\n", "    print(\"\\n--- 調整後の初期在庫 ---\")\n", "    for i, stock in enumerate(adjusted_initial_inventory):\n", "        print(f\"製品 {品番リスト[i]}: {stock}\")\n", "\n", "    # 調整された初期在庫リストを使ってMIPソルバーを実行\n", "    # 修正：グローバル変数を更新\n", "    初期在庫量リスト = adjusted_initial_inventory\n", "    # 修正：solve_mipに引数として初期在庫を渡す\n", "    best_solution, best_cost = solve_mip(初期在庫量リスト)\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "\n", "        # 結果をDataFrameで表示\n", "        品番数 = len(品番リスト)\n", "        df_schedule = pd.DataFrame(best_solution, index=品番リスト, columns=[f'Day_{t+1}' for t in range(期間)])\n", "        print(\"\\n--- 生産スケジュール ---\")\n", "        print(df_schedule)\n", "        \n", "        # プロット\n", "        plot_results(best_solution, 初期在庫量リスト)\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}