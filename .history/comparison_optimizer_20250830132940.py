"""
Production Scheduling Optimization Comparison Tool
Compares Mixed Integer Programming vs Multi-Start Local Search

This script runs both optimization methods and provides detailed comparison
of calculation time and objective values.
"""

import time
import csv
import random
import copy
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import pulp

# Global parameters
在庫コスト単価 = 70
残業コスト単価 = 500
段替えコスト単価 = 500
出荷遅れコスト単価 = 500
定時 = 8 * 60 * 2  # 8時間 * 60分 * 2シフト = 960分
最大残業時間 = 2 * 60 * 2  # 2時間 * 60分 * 2シフト = 240分
段替え時間 = 30  # 分

# Global variables
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20


def read_csv(file_path):
    """CSVファイルからデータを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)
            
            品番リスト = []
            出荷数リスト = []
            収容数リスト = []
            サイクルタイムリスト = []
            込め数リスト = []
            
            # CSVからデータを読み込む
            rows = list(reader)
            for row in rows:
                if len(row) == 0:
                    continue
                品番リスト.append(row[header.index("part_number")])
                出荷数リスト.append(int(row[header.index("shipment")]))
                収容数リスト.append(int(row[header.index("capacity")]))
                
                # サイクルタイムを分単位に変換
                cycle_time_per_unit = float(row[header.index("cycle_time")]) / 60
                サイクルタイムリスト.append(cycle_time_per_unit)
                
                込め数リスト.append(int(row[header.index("cabity")]))

            # 出荷数に基づいて初期在庫量（処理前）をランダムに設定
            初期在庫量リスト = []
            for shipment in 出荷数リスト:
                random_inventory = random.randint(shipment * 3, shipment * 5)
                初期在庫量リスト.append(random_inventory)

            return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    except FileNotFoundError:
        print(f"ファイル '{file_path}' が見つかりません。")
        return None, None, None, None, None, None
    except Exception as e:
        print(f"CSVファイルの読み込み中にエラーが発生しました: {e}")
        return None, None, None, None, None, None


def adjust_initial_inventory(s, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):
    """初期在庫水準を調整するアルゴリズム"""
    global 品番数, 期間
    
    print("\n=== 初期在庫水準の調整アルゴリズム開始 ===")
    
    for iteration in range(max_iterations):
        print(f"--- 調整イテレーション {iteration + 1} ---")
        
        # 各在庫点について在庫量の分布を求める
        inventory_distributions = [[] for _ in range(品番数)]
        
        for sim in range(num_simulations):
            temp_inventory = s[:]
            
            for t in range(期間):
                for i in range(品番数):
                    temp_inventory[i] -= 出荷数リスト[i]
                    inventory_distributions[i].append(temp_inventory[i])
        
        adjustments = [0] * 品番数
        
        # 各在庫点について在庫量の最適調整量r^*を求める
        for i in range(品番数):
            if not inventory_distributions[i]:
                continue
            
            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()
            cumulative_distribution = inventory_counts.cumsum()
            
            # sum_{x <= r-1} f(x) <= h / (h + c)
            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)
            
            best_r = 0
            for r in cumulative_distribution.index:
                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)
                if prob_at_r_minus_1 <= prob_target:
                    best_r = r
                else:
                    break
            
            adjustments[i] = s[i] - best_r
            
        print(f"  今回の調整量: {adjustments}")
        
        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する
        new_s = [s[i] - adjustments[i] for i in range(品番数)]
        
        # 終了条件のチェック
        if all(abs(adj) < 1 for adj in adjustments):
            print("--- アルゴリズムが収束しました。---")
            return s
            
        s = new_s
        print(f"  更新後の初期在庫量: {s}")
        
    print("--- 最大反復回数に到達しました。---")
    return s


def solve_mip(initial_inventory_list_arg):
    """PuLPを用いてMIPを解く関数"""
    
    # 修正：ここではread_csvを呼び出さない
    品番数 = len(品番リスト)
    
    # モデルの定義
    model = pulp.LpProblem("ProductionScheduling", pulp.LpMinimize)
    
    # インデックスの定義
    品目 = range(品番数)
    期間_index = range(期間)
    
    # 決定変数の定義
    Production = pulp.LpVariable.dicts("Production", (品目, 期間_index), lowBound=0, cat='Continuous')
    Inventory = pulp.LpVariable.dicts("Inventory", (品目, 期間_index), lowBound=0, cat='Continuous')
    Shortage = pulp.LpVariable.dicts("Shortage", (品目, 期間_index), lowBound=0, cat='Continuous')
    IsProduced = pulp.LpVariable.dicts("IsProduced", (品目, 期間_index), cat='Binary')
    WorkTime = pulp.LpVariable.dicts("WorkTime", 期間_index, lowBound=0, cat='Continuous')
    Overtime = pulp.LpVariable.dicts("Overtime", 期間_index, lowBound=0, cat='Continuous')

    # 目的関数
    total_cost = pulp.lpSum(
        在庫コスト単価 * Inventory[i][t] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        残業コスト単価 * Overtime[t] for t in 期間_index
    ) + pulp.lpSum(
        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index
    )
    
    model += total_cost, "Total Cost"

    # 制約条件
    bigM = 1000000

    for i in 品目:
        for t in 期間_index:
            if t == 0:
                # 修正：引数で受け取った初期在庫リストを使用
                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]
            else:
                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]
            
            model += Production[i][t] <= bigM * IsProduced[i][t]

    for t in 期間_index:
        model += WorkTime[t] == pulp.lpSum(
            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]
            for i in 品目
        )
        
        model += WorkTime[t] <= 定時 + Overtime[t]
        model += WorkTime[t] <= 定時 + 最大残業時間
        model += Overtime[t] >= WorkTime[t] - 定時
        model += Overtime[t] >= 0

    # Solverの設定
    solver = pulp.GUROBI(msg=False)  # メッセージを非表示に
    
    # 最適化の実行
    model.solve(solver)
    
    if pulp.LpStatus[model.status] == 'Optimal':
        production_schedule = [[0] * 期間 for _ in range(品番数)]
        for i in 品目:
            for t in 期間_index:
                production_schedule[i][t] = pulp.value(Production[i][t])

        return production_schedule, pulp.value(model.objective)

    return None, None


def evaluate(solution, current_initial_inventory):
    """総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数"""
    global 品番数, 期間

    # 最小化コスト
    total_inventory_cost = 0
    total_overtime_cost = 0
    total_setup_cost = 0
    total_shipment_delay_cost = 0

    inventory = current_initial_inventory[:]
    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）
    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）

    inventory_history = [[] for _ in range(品番数)]

    for t in range(期間):
        daily_time = 0
        daily_setup_count = 0

        for i in range(品番数):
            production = solution[t][i]

            # 生産がある場合に段替えをする
            if production > 0:
                daily_setup_count += 1
                setup_time = 30
            else:
                setup_time = 0

            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            daily_time += production_time + setup_time

            inventory[i] += production - 出荷数リスト[i]
            inventory_history[i].append(inventory[i])

            # 出荷遅れコスト
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount

            # 在庫コスト
            if inventory[i] > 0:
                total_inventory_cost += 在庫コスト単価 * inventory[i]

        # 段替えコスト
        total_setup_cost += 段替えコスト単価 * daily_setup_count

        # 残業コスト
        if daily_time > daily_regular_time:
            overtime = daily_time - daily_regular_time
            total_overtime_cost += 残業コスト単価 * overtime

        # 最大稼働時間超過ペナルティ（違反する場合はコストに加算）
        if daily_time > daily_regular_time + max_daily_overtime:
            work_time_penalty = (daily_time - (daily_regular_time + max_daily_overtime)) * (残業コスト単価 * 1000000)
            total_overtime_cost += work_time_penalty

    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost

    return total_cost, inventory_history


def generate_initial_solution(current_initial_inventory):
    """初期解を生成する関数"""
    solution = []
    temp_inventory = current_initial_inventory[:]
    max_daily_work_time = (8 + 2) * 60 * 2

    for t in range(期間):
        daily_productions = [0] * 品番数
        daily_time = 0

        priorities = []
        for i in range(品番数):
            shortage_estimate = max(0, 出荷数リスト[i] - temp_inventory[i])
            priorities.append((shortage_estimate, i))

        priorities.sort(key=lambda x: x[0], reverse=True)

        for shortage_estimate, i in priorities:
            if shortage_estimate > 0:
                setup_time = 30
                remaining_time = max_daily_work_time - daily_time

                if remaining_time > setup_time:
                    cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]
                    if cycle_time_per_unit > 0:
                        max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)

                        if max_producible_by_time > 0:
                            target_production = shortage_estimate + random.randint(0, 50)
                            production = min(target_production, max_producible_by_time)

                            daily_productions[i] = production
                            daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]

        solution.append(daily_productions)

        for i in range(品番数):
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]

    return solution


def get_neighbors(current_solution):
    """近傍解を生成する関数"""
    neighbors = []

    # 2つの生産量を入れ替える
    for _ in range(5):
        neighbor = copy.deepcopy(current_solution)
        t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)
        i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)

        if (t1, i1) != (t2, i2):
            neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]
            neighbors.append(neighbor)

    # 特定の生産量を増減させる
    for _ in range(5):
        neighbor = copy.deepcopy(current_solution)
        t = random.randint(0, 期間 - 1)
        i = random.randint(0, 品番数 - 1)

        change = random.randint(-50, 50)
        neighbor[t][i] = max(0, neighbor[t][i] + change)
        neighbors.append(neighbor)

    return neighbors


def local_search(initial_solution, current_initial_inventory):
    """ローカルサーチを実行する関数"""
    current_solution = initial_solution
    current_cost, _ = evaluate(current_solution, current_initial_inventory)

    while True:
        neighbors = get_neighbors(current_solution)
        best_neighbor = None
        best_neighbor_cost = float('inf')

        for neighbor in neighbors:
            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)
            if neighbor_cost < best_neighbor_cost:
                best_neighbor = neighbor
                best_neighbor_cost = neighbor_cost

        if best_neighbor_cost < current_cost:
            current_solution = best_neighbor
            current_cost = best_neighbor_cost
        else:
            break

    return current_solution, current_cost


def multi_start_local_search(num_starts, current_initial_inventory, verbose=True):
    """多スタートローカルサーチを実行する関数"""
    best_solution_overall = None
    best_cost_overall = float('inf')

    for i in range(num_starts):
        if verbose:
            print(f"--- Start {i+1}/{num_starts} ---")

        initial_solution = generate_initial_solution(current_initial_inventory)

        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory)

        if local_optimal_cost < best_cost_overall:
            best_cost_overall = local_optimal_cost
            best_solution_overall = local_optimal_solution
            if verbose:
                print(f"  New best solution found with total cost: {best_cost_overall:.2f}")

    return best_solution_overall, best_cost_overall
