"""
遺伝的アルゴリズムによる生産スケジューリング最適化
最終版 - 将来需要対応 + 時間制約厳守 + 出荷遅れ削減重視
"""

import random
import numpy as np
import pandas as pd
import csv
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)
            print(f"CSV列名: {header}")
            
            品番リスト = []
            出荷数リスト = []
            収容数リスト = []
            サイクルタイムリスト = []
            込め数リスト = []
            初期在庫量リスト = []
            
            for row in reader:
                if len(row) == 0:
                    continue
                品番リスト.append(row[header.index("part_number")])
                出荷数リスト.append(int(row[header.index("shipment")]))
                収容数リスト.append(int(row[header.index("capacity")]))
                サイクルタイムリスト.append(float(row[header.index("cycle_time")])/60)
                込め数リスト.append(int(row[header.index("cabity")]))
                初期在庫量リスト.append(int(row[header.index("initial_inventory")]))
            
            print(f"読み込み完了: {len(品番リスト)}品番")
            return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    except FileNotFoundError:
        print(f"エラー: ファイル '{file_path}' が見つかりません")
        return None, None, None, None, None, None
    except Exception as e:
        print(f"エラー: CSVファイルの読み込み中に問題が発生しました: {e}")
        return None, None, None, None, None, None

def validate_parameters():
    """パラメータの妥当性をチェック"""
    issues = []
    
    for i in range(品番数):
        # サイクルタイムが0または負の値
        if サイクルタイムリスト[i] <= 0:
            issues.append(f"品番{品番リスト[i]}: サイクルタイムが無効 ({サイクルタイムリスト[i]})")
        
        # 込め数が0または負の値
        if 込め数リスト[i] <= 0:
            issues.append(f"品番{品番リスト[i]}: 込め数が無効 ({込め数リスト[i]})")
        
        # 出荷数が負の値
        if 出荷数リスト[i] < 0:
            issues.append(f"品番{品番リスト[i]}: 出荷数が負の値 ({出荷数リスト[i]})")
        
        # 初期在庫が負の値
        if 初期在庫量リスト[i] < 0:
            issues.append(f"品番{品番リスト[i]}: 初期在庫が負の値 ({初期在庫量リスト[i]})")
    
    if issues:
        print("⚠️ データに問題があります:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ データ検証完了: 問題なし")
        return True

def evaluate(ind):
    """評価関数（時間制約厳守 + 強化された出荷遅れペナルティ）"""
    global 品番数, 期間
    
    total_setup_penalty = 0
    total_overtime_penalty = 0
    total_shortage_penalty = 0
    
    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            # 段替え時間の計算
            if production > 0:
                setup_time = 30
                daily_setup += 1
            else:
                setup_time = 0
            
            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_time += production_time + setup_time
            
            # 在庫更新
            inventory[i] += production - 出荷数リスト[i]
            
            # 強化された出荷遅れペナルティ
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                # 期間が後になるほど重いペナルティ + 不足量に比例
                delay_penalty = 50000 * shortage_amount * (t + 1)
                total_shortage_penalty += delay_penalty
                inventory[i] = 0
        
        # 時間制約違反に対する非常に大きなペナルティ
        if daily_time > max_daily_time:
            overtime = daily_time - max_daily_time
            total_overtime_penalty += 1000000 * overtime
        
        # 段替え制約ペナルティ
        if daily_setup > 5:
            total_setup_penalty += 10000 * (daily_setup - 5)
    
    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty
    return total_penalty,

def generate_ind():
    """個体生成関数（将来需要対応版 - 時間制約厳守）"""
    productions = []
    temp_inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間を厳守

    for t in range(期間):
        daily_time = 0
        daily_productions = [0] * 品番数

        # 優先度を計算（現在+将来需要を考慮）
        priorities = []
        for i in range(品番数):
            # 現在の不足分
            current_shortage = max(0, 出荷数リスト[i] - temp_inventory[i])

            # 将来2-3期間の需要を先読み（バッファ在庫の概念）
            future_periods = min(3, 期間 - t)  # 残り期間を考慮
            future_demand = 出荷数リスト[i] * future_periods

            # 将来需要に対する不足リスク
            future_shortage = max(0, future_demand - temp_inventory[i])

            # 総合リスク = 現在不足 + 将来不足リスク（重み付き）
            total_risk = (current_shortage * 3 + future_shortage * 1) * 出荷数リスト[i]
            priorities.append((total_risk, i))

        # リスクの高い順にソート
        priorities.sort(reverse=True)

        # 優先度順に生産を検討（時間制約を厳守）
        for risk, i in priorities:
            # 現在の不足分
            current_shortage = max(0, 出荷数リスト[i] - temp_inventory[i])

            # 将来需要を考慮した目標在庫レベル
            future_periods = min(2, 期間 - t)  # 2期間先まで考慮
            target_inventory = 出荷数リスト[i] * (1 + future_periods * 0.5)  # 現在+将来の50%
            total_shortage = max(0, target_inventory - temp_inventory[i])

            # 最低でも現在不足分は生産、余裕があれば将来分も
            if current_shortage > 0 or (total_shortage > 0 and temp_inventory[i] < 出荷数リスト[i] * 1.5):
                setup_time = 30
                remaining_time = max_daily_time - daily_time - setup_time

                if remaining_time > 0 and サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                    cycle_time_per_unit = サイクルタイムリスト[i] * 込め数リスト[i]
                    max_producible = int(remaining_time / cycle_time_per_unit)

                    if max_producible > 0:
                        # 生産量の決定：現在不足分は必須、将来分は余裕があれば
                        min_production = max(current_shortage, 出荷数リスト[i] // 2)  # 最低限
                        max_production = min(total_shortage, max_producible)  # 時間制約内

                        if min_production <= max_production:
                            production = random.randint(min_production, max_production)
                        else:
                            production = min(min_production, max_producible)

                        production_time = setup_time + production * cycle_time_per_unit

                        # 時間制約を厳守（絶対に超えない）
                        if daily_time + production_time <= max_daily_time:
                            daily_productions[i] = production
                            daily_time += production_time

        # 生産量を記録し、在庫を更新
        for i in range(品番数):
            productions.append(daily_productions[i])
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]
            temp_inventory[i] = max(0, temp_inventory[i])

    return creator.Individual(productions)

def mutate(ind):
    """突然変異関数（遅れ削減重視版）"""
    max_daily_time = (8 + 2) * 60 * 2

    # 現在の解の出荷遅れを評価
    temp_inventory = 初期在庫量リスト[:]
    delay_periods = []

    for t in range(期間):
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            temp_inventory[i] += production - 出荷数リスト[i]

            if temp_inventory[i] < 0:
                delay_periods.append((t, i, abs(temp_inventory[i])))
                temp_inventory[i] = 0

    # 遅れが発生している期間を優先的に改善
    if delay_periods:
        # 最も遅れの大きい期間を選択
        delay_periods.sort(key=lambda x: x[2], reverse=True)
        target_period, target_product, delay_amount = delay_periods[0]

        # その期間より前の期間で生産を増やす
        for t in range(max(0, target_period - 2), target_period):
            idx = t * 品番数 + target_product

            # 時間制約内で生産量を増やす
            if random.random() < 0.7:  # 70%の確率で改善
                increase = random.randint(50, min(300, delay_amount))
                ind[idx] = min(ind[idx] + increase, 2000)

    # 通常の突然変異も実行
    for t in range(期間):
        if random.random() < 0.08:  # 8%の確率
            for i in range(品番数):
                if random.random() < 0.25:
                    idx = t * 品番数 + i
                    change = random.randint(-80, 120)  # 増産寄り
                    ind[idx] = max(0, min(2000, ind[idx] + change))

    return ind,

def plot_results(best_individual):
    """結果をプロットする関数（出荷遅れ分析付き）"""
    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

    # 各期間の結果を計算
    total_inventory_per_period = []
    total_production_time_per_period = []
    total_setup_times_per_period = []
    total_shipment_delay_per_period = []

    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2

    for t in range(期間):
        daily_inventory = 0
        daily_production_time = 0
        daily_setup_times = 0
        daily_shipment_delay = 0

        for i in range(品番数):
            idx = t * 品番数 + i
            production = best_individual[idx]

            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0

            # 段替え回数の計算
            if production > 0:
                daily_setup_times += 1
                setup_time = 30
            else:
                setup_time = 0

            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_production_time += production_time + setup_time

            # 在庫更新と出荷遅れ計算
            inventory[i] += production - 出荷数リスト[i]

            # 出荷遅れの計算
            if inventory[i] < 0:
                daily_shipment_delay += abs(inventory[i])
                inventory[i] = 0

            daily_inventory += inventory[i]

        total_inventory_per_period.append(daily_inventory)
        total_production_time_per_period.append(daily_production_time)
        total_setup_times_per_period.append(daily_setup_times)
        total_shipment_delay_per_period.append(daily_shipment_delay)

    # プロット作成（2x2レイアウト）
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('生産スケジューリング最適化結果（最終版）', fontsize=16, fontweight='bold')

    periods = list(range(1, 期間 + 1))

    # 1. 各期間の総在庫量
    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各期間の総在庫量', fontweight='bold')
    axes[0, 0].set_xlabel('期間')
    axes[0, 0].set_ylabel('総在庫量 (個)')
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 各期間の総生産時間（制限ラインを追加）
    bars_time = axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
    axes[0, 1].axhline(y=max_daily_time, color='red', linestyle='--', alpha=0.8,
                       label=f'時間制限 ({max_daily_time}分)')

    # 制限を超えた期間を赤色でハイライト
    for i, (period, time) in enumerate(zip(periods, total_production_time_per_period)):
        if time > max_daily_time:
            bars_time[i].set_color('red')

    axes[0, 1].set_title('各期間の総生産時間', fontweight='bold')
    axes[0, 1].set_xlabel('期間')
    axes[0, 1].set_ylabel('総生産時間 (分)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 各期間の総段替え回数
    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
    axes[1, 0].axhline(y=5, color='red', linestyle='--', alpha=0.8, label='上限 (5回)')
    axes[1, 0].set_title('各期間の総段替え回数', fontweight='bold')
    axes[1, 0].set_xlabel('期間')
    axes[1, 0].set_ylabel('段替え回数')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 各期間の総出荷遅れ量
    bars_delay = axes[1, 1].bar(periods, total_shipment_delay_per_period, color='orange', alpha=0.7)
    axes[1, 1].set_title('各期間の総出荷遅れ量', fontweight='bold')
    axes[1, 1].set_xlabel('期間')
    axes[1, 1].set_ylabel('出荷遅れ量 (個)')
    axes[1, 1].grid(True, alpha=0.3)

    # 出荷遅れがある期間を赤色で強調表示
    for i, (period, delay) in enumerate(zip(periods, total_shipment_delay_per_period)):
        if delay > 0:
            bars_delay[i].set_color('red')
            bars_delay[i].set_alpha(0.8)

    # 出荷遅れゼロのラインを追加
    axes[1, 1].axhline(y=0, color='green', linestyle='-', alpha=0.8, label='遅れなし')
    axes[1, 1].legend()

    plt.tight_layout()
    plt.show()

    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period

def analyze_results(inventory_data, production_time_data, setup_data, shipment_delay_data):
    """結果の詳細分析を行う関数"""
    max_daily_time = (8 + 2) * 60 * 2

    print("\n" + "="*60)
    print("📊 詳細分析結果")
    print("="*60)

    # 出荷遅れ分析
    total_delay = sum(shipment_delay_data)
    delay_periods = sum(1 for x in shipment_delay_data if x > 0)
    max_delay = max(shipment_delay_data) if shipment_delay_data else 0

    print(f"\n🚚 出荷遅れ分析:")
    print(f"  総出荷遅れ量: {total_delay:.1f} 個")
    print(f"  出荷遅れ発生期間: {delay_periods} / {期間} 期間")
    print(f"  最大出荷遅れ量: {max_delay:.1f} 個")
    print(f"  出荷遅れ率: {(delay_periods / 期間 * 100):.1f}%")

    if delay_periods > 0:
        avg_delay = total_delay / delay_periods
        print(f"  平均出荷遅れ量（遅れ発生期間のみ）: {avg_delay:.1f} 個")
        delay_periods_list = [i+1 for i, x in enumerate(shipment_delay_data) if x > 0]
        print(f"  出荷遅れ発生期間: {delay_periods_list}")
    else:
        print("  ✅ 出荷遅れは発生していません！")

    # 時間制約分析
    time_violations = sum(1 for x in production_time_data if x > max_daily_time)
    max_time = max(production_time_data) if production_time_data else 0
    avg_time = sum(production_time_data) / len(production_time_data) if production_time_data else 0

    print(f"\n⏰ 時間制約分析:")
    print(f"  時間制約違反: {time_violations} / {期間} 期間")
    print(f"  制約遵守率: {((期間 - time_violations) / 期間 * 100):.1f}%")
    print(f"  最大生産時間: {max_time:.1f} 分")
    print(f"  平均生産時間: {avg_time:.1f} 分")
    print(f"  時間利用率: {(avg_time / max_daily_time * 100):.1f}%")

    if time_violations > 0:
        violation_periods = [i+1 for i, x in enumerate(production_time_data) if x > max_daily_time]
        print(f"  ⚠️ 時間制約違反期間: {violation_periods}")
    else:
        print("  ✅ 時間制約を完全に遵守しています！")

    # 段替え分析
    setup_violations = sum(1 for x in setup_data if x > 5)
    max_setup = max(setup_data) if setup_data else 0
    avg_setup = sum(setup_data) / len(setup_data) if setup_data else 0

    print(f"\n🔧 段替え分析:")
    print(f"  段替え制約違反: {setup_violations} / {期間} 期間")
    print(f"  最大段替え回数: {max_setup} 回")
    print(f"  平均段替え回数: {avg_setup:.1f} 回")

    if setup_violations > 0:
        setup_violation_periods = [i+1 for i, x in enumerate(setup_data) if x > 5]
        print(f"  ⚠️ 段替え制約違反期間: {setup_violation_periods}")
    else:
        print("  ✅ 段替え制約を完全に遵守しています！")

    # 在庫分析
    total_inventory = sum(inventory_data)
    max_inventory = max(inventory_data) if inventory_data else 0
    avg_inventory = total_inventory / len(inventory_data) if inventory_data else 0

    print(f"\n📦 在庫分析:")
    print(f"  総在庫量: {total_inventory:.1f} 個")
    print(f"  最大在庫量: {max_inventory:.1f} 個")
    print(f"  平均在庫量: {avg_inventory:.1f} 個")

    # 総合評価
    print(f"\n🎯 総合評価:")
    if delay_periods == 0 and time_violations == 0 and setup_violations == 0:
        print("  🏆 完璧な解です！全ての制約を満たし、出荷遅れもありません。")
    elif delay_periods == 0:
        print("  ✅ 優秀な解です！出荷遅れはありませんが、一部制約違反があります。")
    elif delay_periods <= 期間 * 0.1:
        print("  👍 良好な解です！出荷遅れは最小限に抑えられています。")
    else:
        print("  ⚠️ 改善の余地があります。出荷遅れの削減が必要です。")

    print("="*60)

def main():
    """メイン実行関数"""
    global 品番数, 期間

    print("🚀 GA生産スケジューリング最適化システム（最終版）")
    print("=" * 60)

    # CSVファイルの読み込み
    result = read_csv('data_ga.csv')
    if result[0] is None:
        print("❌ CSVファイルの読み込みに失敗しました")
        return None, None

    品番数 = len(品番リスト)
    期間 = 20

    # データ検証
    if not validate_parameters():
        print("❌ データに問題があるため、処理を中止します")
        return None, None

    print(f"\n📋 設定情報:")
    print(f"  品番数: {品番数}")
    print(f"  期間: {期間}")
    print(f"  1日の最大稼働時間: {(8 + 2) * 60 * 2} 分")
    print(f"  最大段替え回数: 5 回/日")

    # DEAP設定
    if hasattr(creator, 'FitnessMin'):
        del creator.FitnessMin
    if hasattr(creator, 'Individual'):
        del creator.Individual

    creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
    creator.create("Individual", list, fitness=creator.FitnessMin)

    toolbox = base.Toolbox()
    toolbox.register("individual", generate_ind)
    toolbox.register("population", tools.initRepeat, list, toolbox.individual)
    toolbox.register("evaluate", evaluate)
    toolbox.register("mate", tools.cxTwoPoint)
    toolbox.register("mutate", mutate)
    toolbox.register("select", tools.selTournament, tournsize=3)

    # GAパラメータ（遅れ削減重視）
    population_size = 150  # 集団サイズを増加
    generations = 100      # 世代数を増加
    cxpb = 0.6
    mutpb = 0.3           # 突然変異率を増加

    print(f"\n🧬 GAパラメータ:")
    print(f"  集団サイズ: {population_size}")
    print(f"  世代数: {generations}")
    print(f"  交叉率: {cxpb}")
    print(f"  突然変異率: {mutpb}")

    # 初期集団生成
    print(f"\n⚙️ 初期集団生成中...")
    population = toolbox.population(n=population_size)
    hof = tools.HallOfFame(1)
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean)
    stats.register("min", np.min)
    stats.register("max", np.max)

    print("🔄 GA最適化開始...")

    # GA実行
    population, logbook = algorithms.eaSimple(
        population, toolbox,
        cxpb=cxpb, mutpb=mutpb, ngen=generations,
        stats=stats, halloffame=hof, verbose=True
    )

    # 結果の表示
    best_ind = hof[0]
    best_fitness = best_ind.fitness.values[0]

    print(f"\n🎯 最適化結果:")
    print(f"  最良個体のペナルティ: {best_fitness:.2f}")

    # 結果をプロット
    print(f"\n📊 結果をプロット中...")
    inventory_data, production_time_data, setup_data, shipment_delay_data = plot_results(best_ind)

    # 詳細分析
    analyze_results(inventory_data, production_time_data, setup_data, shipment_delay_data)

    return best_ind, logbook

if __name__ == "__main__":
    print("🌟 GA生産スケジューリング最適化システム 🌟")
    print("最終版 - 将来需要対応 + 時間制約厳守 + 出荷遅れ削減重視")
    print("=" * 70)

    try:
        best_solution, log = main()

        if best_solution is not None:
            print(f"\n🎉 最適化完了！")
            print("📈 グラフと詳細分析で結果を確認してください。")
        else:
            print(f"\n❌ 最適化に失敗しました。")

    except KeyboardInterrupt:
        print(f"\n⏹️ ユーザーによって中断されました。")
    except Exception as e:
        print(f"\n💥 エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()

    print("\n" + "=" * 70)
    print("プログラム終了")
