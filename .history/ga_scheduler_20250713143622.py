"""
Enhanced GA Production Scheduler with Cost Minimization
======================================================
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from dataclasses import dataclass
from typing import List
import random
from deap import base, creator, tools, algorithms
import warnings
warnings.filterwarnings('ignore')

np.random.seed(42)
random.seed(42)

@dataclass
class ProductionData:
    part_numbers: List[str]
    shipments: List[int]
    capacities: List[int]
    cycle_times: List[float]
    cavities: List[int]
    initial_inventories: List[int]

class CostParameters:
    def __init__(self, setup_cost=5000.0, overtime_cost=100.0, inventory_cost=10.0, shortage_penalty=50000.0):
        self.setup_cost_per_changeover = setup_cost
        self.overtime_cost_per_minute = overtime_cost
        self.inventory_holding_cost_per_unit_day = inventory_cost
        self.shortage_penalty_per_unit = shortage_penalty

class GAParameters:
    def __init__(self, population_size=150, generations=100, crossover_probability=0.7,
                 mutation_probability=0.2, tournament_size=3, elite_size=5):
        self.population_size = population_size
        self.generations = generations
        self.crossover_probability = crossover_probability
        self.mutation_probability = mutation_probability
        self.tournament_size = tournament_size
        self.elite_size = elite_size

class ProductionConstraints:
    def __init__(self, max_daily_time_minutes=1200, max_daily_setups=5, planning_periods=20):
        self.max_daily_time_minutes = max_daily_time_minutes
        self.max_daily_setups = max_daily_setups
        self.planning_periods = planning_periods

class EnhancedGAProductionScheduler:
    def __init__(self, production_data, cost_params, constraints, ga_params):
        self.production_data = production_data
        self.cost_params = cost_params
        self.constraints = constraints
        self.ga_params = ga_params
        self.num_products = len(production_data.part_numbers)
        self.num_periods = constraints.planning_periods
        self.best_solution = None
        self.best_cost = float('inf')
        self.cost_breakdown = None
        self._setup_deap()

    def _setup_deap(self):
        if hasattr(creator, 'FitnessMin'): del creator.FitnessMin
        if hasattr(creator, 'Individual'): del creator.Individual
        creator.create("FitnessMin", base.Fitness, weights=(-1.0,))
        creator.create("Individual", list, fitness=creator.FitnessMin)
        self.toolbox = base.Toolbox()
        self.toolbox.register("individual", self._generate_individual)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        self.toolbox.register("evaluate", self._evaluate_individual)
        self.toolbox.register("mate", self._crossover)
        self.toolbox.register("mutate", self._mutate)
        self.toolbox.register("select", tools.selTournament, tournsize=self.ga_params.tournament_size)
    
    def _generate_individual(self):
        individual = []
        temp_inventory = self.production_data.initial_inventories[:]

        for period in range(self.num_periods):
            daily_time = 0
            daily_setups = 0
            period_production = [0] * self.num_products

            # Calculate priorities based on shortage risk
            priorities = [(max(0, self.production_data.shipments[i] - temp_inventory[i]) *
                          self.production_data.shipments[i], i) for i in range(self.num_products)]
            priorities.sort(reverse=True)

            for risk, product_idx in priorities:
                if daily_setups >= self.constraints.max_daily_setups:
                    break

                shortage = max(0, self.production_data.shipments[product_idx] - temp_inventory[product_idx])
                if shortage <= 0:
                    continue

                cycle_time = self.production_data.cycle_times[product_idx]
                cavity = self.production_data.cavities[product_idx]
                if cycle_time <= 0 or cavity <= 0:
                    continue

                time_per_unit = cycle_time / cavity
                setup_time = 30
                remaining_time = self.constraints.max_daily_time_minutes - daily_time - setup_time

                if remaining_time <= 0:
                    break

                max_producible = int(remaining_time / time_per_unit)
                if max_producible <= 0:
                    continue

                target_production = min(shortage * random.randint(1, 3), max_producible)
                production_quantity = random.randint(shortage, max(shortage, target_production))
                production_time = setup_time + production_quantity * time_per_unit

                if daily_time + production_time <= self.constraints.max_daily_time_minutes:
                    period_production[product_idx] = production_quantity
                    daily_time += production_time
                    daily_setups += 1
                    temp_inventory[product_idx] += production_quantity

            for i in range(self.num_products):
                temp_inventory[i] = max(0, temp_inventory[i] - self.production_data.shipments[i])

            individual.extend(period_production)

        return creator.Individual(individual)
    
    def _evaluate_individual(self, individual):
        setup_cost = overtime_cost = inventory_cost = shortage_penalty = 0.0
        inventory = self.production_data.initial_inventories[:]

        for period in range(self.num_periods):
            daily_time = daily_setups = 0

            for product_idx in range(self.num_products):
                idx = period * self.num_products + product_idx
                production = individual[idx]

                if production > 0:
                    daily_setups += 1
                    cycle_time = self.production_data.cycle_times[product_idx]
                    cavity = self.production_data.cavities[product_idx]
                    if cycle_time > 0 and cavity > 0:
                        daily_time += 30 + production * (cycle_time / cavity)

                inventory[product_idx] += production - self.production_data.shipments[product_idx]

                if inventory[product_idx] < 0:
                    shortage_penalty += abs(inventory[product_idx]) * self.cost_params.shortage_penalty_per_unit
                    inventory[product_idx] = 0

                inventory_cost += max(0, inventory[product_idx]) * self.cost_params.inventory_holding_cost_per_unit_day

            setup_cost += daily_setups * self.cost_params.setup_cost_per_changeover

            if daily_time > self.constraints.max_daily_time_minutes:
                overtime_cost += (daily_time - self.constraints.max_daily_time_minutes) * self.cost_params.overtime_cost_per_minute

        return (setup_cost + overtime_cost + inventory_cost + shortage_penalty,)
    
    def _crossover(self, ind1, ind2):
        for period in range(self.num_periods):
            if random.random() < 0.5:
                start_idx = period * self.num_products
                end_idx = start_idx + self.num_products
                ind1[start_idx:end_idx], ind2[start_idx:end_idx] = ind2[start_idx:end_idx], ind1[start_idx:end_idx]
        return ind1, ind2

    def _mutate(self, individual):
        for i in range(len(individual)):
            if random.random() < 0.1:
                current_value = individual[i]
                if current_value > 0:
                    individual[i] = max(0, current_value + random.randint(-50, 50))
                elif random.random() < 0.3:
                    product_idx = i % self.num_products
                    individual[i] = random.randint(0, self.production_data.shipments[product_idx] * 2)
        return (individual,)

    def optimize(self):
        population = self.toolbox.population(n=self.ga_params.population_size)
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("min", np.min)
        hof = tools.HallOfFame(self.ga_params.elite_size)

        population, logbook = algorithms.eaSimple(
            population, self.toolbox,
            cxpb=self.ga_params.crossover_probability,
            mutpb=self.ga_params.mutation_probability,
            ngen=self.ga_params.generations,
            stats=stats,
            halloffame=hof,
            verbose=True
        )

        self.best_solution = hof[0]
        self.best_cost = hof[0].fitness.values[0]
        self.cost_breakdown = self._calculate_cost_breakdown(self.best_solution)

        print("===============================")
        for cost_type, amount in self.cost_breakdown.items():
            print(f"{cost_type}: ¥{amount:,.2f}")

        return self.best_solution, logbook

    def _calculate_cost_breakdown(self, individual):
        setup_cost = overtime_cost = inventory_cost = shortage_penalty = 0.0
        inventory = self.production_data.initial_inventories[:]

        for period in range(self.num_periods):
            daily_time = daily_setups = 0

            for product_idx in range(self.num_products):
                idx = period * self.num_products + product_idx
                production = individual[idx]

                if production > 0:
                    daily_setups += 1
                    cycle_time = self.production_data.cycle_times[product_idx]
                    cavity = self.production_data.cavities[product_idx]
                    if cycle_time > 0 and cavity > 0:
                        daily_time += 30 + production * (cycle_time / cavity)

                inventory[product_idx] += production - self.production_data.shipments[product_idx]

                if inventory[product_idx] < 0:
                    shortage_penalty += abs(inventory[product_idx]) * self.cost_params.shortage_penalty_per_unit
                    inventory[product_idx] = 0

                inventory_cost += max(0, inventory[product_idx]) * self.cost_params.inventory_holding_cost_per_unit_day

            setup_cost += daily_setups * self.cost_params.setup_cost_per_changeover

            if daily_time > self.constraints.max_daily_time_minutes:
                overtime_cost += (daily_time - self.constraints.max_daily_time_minutes) * self.cost_params.overtime_cost_per_minute

        return {
            'Setup Cost': setup_cost,
            'Overtime Cost': overtime_cost,
            'Inventory Cost': inventory_cost,
            'Total Cost': setup_cost + overtime_cost + inventory_cost + shortage_penalty
        }

    def plot_results(self, save_path=None):
        """Plot optimization results with cost analysis"""
        if self.best_solution is None:
            raise ValueError("No solution available. Run optimize() first.")

        print("\n=== 結果のプロット ===")

        # Data collection
        total_inventory_per_period = []
        total_production_time_per_period = []
        total_setup_times_per_period = []
        total_shipment_delay_per_period = []
        setup_cost_per_period = []
        overtime_cost_per_period = []
        inventory_cost_per_period = []
        shortage_cost_per_period = []

        inventory = self.production_data.initial_inventories[:]
        max_daily_time = self.constraints.max_daily_time_minutes

        for period in range(self.num_periods):
            daily_inventory = 0
            daily_production_time = 0
            daily_setup_times = 0
            daily_shipment_delay = 0

            for product_idx in range(self.num_products):
                idx = period * self.num_products + product_idx
                production = self.best_solution[idx]

                if production > 0:
                    daily_setup_times += 1
                    cycle_time = self.production_data.cycle_times[product_idx]
                    cavity = self.production_data.cavities[product_idx]
                    if cycle_time > 0 and cavity > 0:
                        daily_production_time += 30 + production * (cycle_time / cavity)

                inventory[product_idx] += production - self.production_data.shipments[product_idx]

                if inventory[product_idx] < 0:
                    daily_shipment_delay += abs(inventory[product_idx])
                    inventory[product_idx] = 0

                daily_inventory += inventory[product_idx]

            total_inventory_per_period.append(daily_inventory)
            total_production_time_per_period.append(daily_production_time)
            total_setup_times_per_period.append(daily_setup_times)
            total_shipment_delay_per_period.append(daily_shipment_delay)

            # Cost calculations
            setup_cost_per_period.append(daily_setup_times * self.cost_params.setup_cost_per_changeover)
            overtime_cost = max(0, daily_production_time - max_daily_time) * self.cost_params.overtime_cost_per_minute
            overtime_cost_per_period.append(overtime_cost)
            inventory_cost_per_period.append(daily_inventory * self.cost_params.inventory_holding_cost_per_unit_day)
            shortage_cost_per_period.append(daily_shipment_delay * self.cost_params.shortage_penalty_per_unit)

        # Create plots (3x2 layout)
        fig, axes = plt.subplots(3, 2, figsize=(16, 18))
        periods = list(range(1, self.num_periods + 1))

        # 1. Total inventory per period
        axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
        axes[0, 0].set_title('Total Inventory per Period', fontweight='bold')
        axes[0, 0].set_xlabel('Period')
        axes[0, 0].set_ylabel('Total Inventory (units)')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. Total production time per period
        axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
        axes[0, 1].axhline(y=max_daily_time, color='red', linestyle='--', alpha=0.8,
                          label=f'Limit ({max_daily_time} min)')
        axes[0, 1].set_title('Total Production Time per Period', fontweight='bold')
        axes[0, 1].set_xlabel('Period')
        axes[0, 1].set_ylabel('Total Time (minutes)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 3. Total setup times per period
        axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
        axes[1, 0].axhline(y=self.constraints.max_daily_setups, color='red', linestyle='--',
                          alpha=0.8, label=f'Limit ({self.constraints.max_daily_setups})')
        axes[1, 0].set_title('Total Setup Times per Period', fontweight='bold')
        axes[1, 0].set_xlabel('Period')
        axes[1, 0].set_ylabel('Setup Times')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 4. Total shipment delay per period
        axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)
        axes[1, 1].set_title('Total Shipment Delay per Period', fontweight='bold')
        axes[1, 1].set_xlabel('Period')
        axes[1, 1].set_ylabel('Delay (units)')
        axes[1, 1].grid(True, alpha=0.3)

        # 5. Cost breakdown per period (stacked bar)
        width = 0.8
        axes[2, 0].bar(periods, setup_cost_per_period, width, label='Setup Cost',
                      color='lightcoral', alpha=0.8)
        axes[2, 0].bar(periods, overtime_cost_per_period, width, bottom=setup_cost_per_period,
                      label='Overtime Cost', color='lightblue', alpha=0.8)

        bottom1 = [setup_cost_per_period[i] + overtime_cost_per_period[i] for i in range(len(periods))]
        axes[2, 0].bar(periods, inventory_cost_per_period, width, bottom=bottom1,
                      label='Inventory Cost', color='lightgreen', alpha=0.8)

        bottom2 = [bottom1[i] + inventory_cost_per_period[i] for i in range(len(periods))]
        axes[2, 0].bar(periods, shortage_cost_per_period, width, bottom=bottom2,
                      label='Shortage Cost', color='orange', alpha=0.8)

        axes[2, 0].set_title('Cost Breakdown per Period', fontweight='bold')
        axes[2, 0].set_xlabel('Period')
        axes[2, 0].set_ylabel('Cost (¥)')
        axes[2, 0].legend()
        axes[2, 0].grid(True, alpha=0.3)

        # 6. Total cost trend
        total_cost_per_period = [setup_cost_per_period[i] + overtime_cost_per_period[i] +
                               inventory_cost_per_period[i] + shortage_cost_per_period[i]
                               for i in range(len(periods))]

        axes[2, 1].plot(periods, total_cost_per_period, marker='o', linewidth=2,
                       markersize=6, color='darkred')
        axes[2, 1].fill_between(periods, total_cost_per_period, alpha=0.3, color='darkred')
        axes[2, 1].set_title('Total Cost Trend per Period', fontweight='bold')
        axes[2, 1].set_xlabel('Period')
        axes[2, 1].set_ylabel('Total Cost (¥)')
        axes[2, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to: {save_path}")

        plt.show()

        # Print statistics
        delay_violations = sum(1 for x in total_shipment_delay_per_period if x > 0)
        time_violations = sum(1 for x in total_production_time_per_period if x > max_daily_time)
        setup_violations = sum(1 for x in total_setup_times_per_period if x > self.constraints.max_daily_setups)

        total_setup_cost = sum(setup_cost_per_period)
        total_overtime_cost = sum(overtime_cost_per_period)
        total_inventory_cost = sum(inventory_cost_per_period)
        total_shortage_cost = sum(shortage_cost_per_period)
        grand_total_cost = total_setup_cost + total_overtime_cost + total_inventory_cost + total_shortage_cost

        print(f"\n=== Constraint Violations ===")
        print(f"Time constraint violations: {time_violations} periods")
        print(f"Setup constraint violations: {setup_violations} periods")
        print(f"Shipment delay violations: {delay_violations} periods")

        return {
            'periods': periods,
            'inventory': total_inventory_per_period,
            'production_time': total_production_time_per_period,
            'setup_times': total_setup_times_per_period,
            'shipment_delay': total_shipment_delay_per_period,
            'costs': {
                'setup': total_setup_cost,
                'overtime': total_overtime_cost,
                'inventory': total_inventory_cost,
                'shortage': total_shortage_cost,
                'total': grand_total_cost
            }
        }


def load_production_data(csv_file: str) -> ProductionData:
    """Load production data from CSV file"""
    try:
        df = pd.read_csv(csv_file)

        return ProductionData(
            part_numbers=df['part_number'].tolist(),
            shipments=df['shipment'].tolist(),
            capacities=df['capacity'].tolist(),
            cycle_times=df['cycle_time'].tolist(),
            cavities=df['cabity'].tolist(),  # Note: keeping original column name
            initial_inventories=df['initial_inventory'].tolist()
        )
    except Exception as e:
        print(f"Error loading data from {csv_file}: {e}")
        return None


def create_experiment_config(base_params, variations):
    configs = []
    for variation_name, param_changes in variations.items():
        config = GAParameters(
            population_size=param_changes.get('population_size', base_params.population_size),
            generations=param_changes.get('generations', base_params.generations),
            crossover_probability=param_changes.get('crossover_probability', base_params.crossover_probability),
            mutation_probability=param_changes.get('mutation_probability', base_params.mutation_probability),
            tournament_size=param_changes.get('tournament_size', base_params.tournament_size),
            elite_size=param_changes.get('elite_size', base_params.elite_size)
        )
        configs.append((variation_name, config))
    return configs


def run_single_experiment(production_data, cost_params=None, constraints=None, ga_params=None,
                         experiment_name="default", setup_cost=5000.0, overtime_cost=100.0,
                         inventory_cost=10.0, shortage_penalty=50000.0):

    if cost_params is None:
        cost_params = CostParameters(setup_cost, overtime_cost, inventory_cost, shortage_penalty)
    if constraints is None:
        constraints = ProductionConstraints()
    if ga_params is None:
        ga_params = GAParameters()

    scheduler = EnhancedGAProductionScheduler(production_data, cost_params, constraints, ga_params)
    scheduler.optimize()

    return {
        'experiment_name': experiment_name,
        'best_cost': scheduler.best_cost,
        'cost_breakdown': scheduler.cost_breakdown,
        'scheduler': scheduler
    }


def optimize_with_costs(data_file='data_ga.csv', setup_cost=5000.0, overtime_cost=100.0,
                       inventory_cost=10.0, shortage_penalty=50000.0, population_size=150,
                       generations=100, crossover_prob=0.7, mutation_prob=0.2,
                       max_daily_time=1200, max_daily_setups=5, planning_periods=20,
                       experiment_name="custom"):
    """Convenient function to run optimization with all parameters as arguments"""

    production_data = load_production_data(data_file)
    if production_data is None:
        raise ValueError(f"Failed to load data from {data_file}")

    cost_params = CostParameters(setup_cost, overtime_cost, inventory_cost, shortage_penalty)
    constraints = ProductionConstraints(max_daily_time, max_daily_setups, planning_periods)
    ga_params = GAParameters(population_size, generations, crossover_prob, mutation_prob)

    return run_single_experiment(production_data, cost_params, constraints, ga_params, experiment_name)
