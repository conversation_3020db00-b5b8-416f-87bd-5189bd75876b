"""
遺伝的アルゴリズムによる生産スケジューリング最適化
最終版 - 将来需要対応 + 時間制約厳守 + 出荷遅れ削減重視
"""

import random
import numpy as np
import pandas as pd
import csv
from deap import base, creator, tools, algorithms
import matplotlib.pyplot as plt
import japanize_matplotlib

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 20

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            header = next(reader)
            print(f"CSV列名: {header}")
            
            品番リスト = []
            出荷数リスト = []
            収容数リスト = []
            サイクルタイムリスト = []
            込め数リスト = []
            初期在庫量リスト = []
            
            for row in reader:
                if len(row) == 0:
                    continue
                品番リスト.append(row[header.index("part_number")])
                出荷数リスト.append(int(row[header.index("shipment")]))
                収容数リスト.append(int(row[header.index("capacity")]))
                サイクルタイムリスト.append(float(row[header.index("cycle_time")])/60)
                込め数リスト.append(int(row[header.index("cabity")]))
                初期在庫量リスト.append(int(row[header.index("initial_inventory")]))
            
            print(f"読み込み完了: {len(品番リスト)}品番")
            return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    except FileNotFoundError:
        print(f"エラー: ファイル '{file_path}' が見つかりません")
        return None, None, None, None, None, None
    except Exception as e:
        print(f"エラー: CSVファイルの読み込み中に問題が発生しました: {e}")
        return None, None, None, None, None, None

def validate_parameters():
    """パラメータの妥当性をチェック"""
    issues = []
    
    for i in range(品番数):
        # サイクルタイムが0または負の値
        if サイクルタイムリスト[i] <= 0:
            issues.append(f"品番{品番リスト[i]}: サイクルタイムが無効 ({サイクルタイムリスト[i]})")
        
        # 込め数が0または負の値
        if 込め数リスト[i] <= 0:
            issues.append(f"品番{品番リスト[i]}: 込め数が無効 ({込め数リスト[i]})")
        
        # 出荷数が負の値
        if 出荷数リスト[i] < 0:
            issues.append(f"品番{品番リスト[i]}: 出荷数が負の値 ({出荷数リスト[i]})")
        
        # 初期在庫が負の値
        if 初期在庫量リスト[i] < 0:
            issues.append(f"品番{品番リスト[i]}: 初期在庫が負の値 ({初期在庫量リスト[i]})")
    
    if issues:
        print("⚠️ データに問題があります:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ データ検証完了: 問題なし")
        return True

def evaluate(ind):
    """評価関数（時間制約厳守 + 強化された出荷遅れペナルティ）"""
    global 品番数, 期間
    
    total_setup_penalty = 0
    total_overtime_penalty = 0
    total_shortage_penalty = 0
    
    inventory = 初期在庫量リスト[:]
    max_daily_time = (8 + 2) * 60 * 2  # 1日の最大稼働時間（分）
    
    for t in range(期間):
        daily_time = 0
        daily_setup = 0
        
        for i in range(品番数):
            idx = t * 品番数 + i
            production = ind[idx]
            
            # 在庫が十分にある場合は生産しない
            if inventory[i] >= 出荷数リスト[i]:
                production = 0
            
            # 段替え時間の計算
            if production > 0:
                setup_time = 30
                daily_setup += 1
            else:
                setup_time = 0
            
            # 生産時間の計算
            if サイクルタイムリスト[i] > 0 and 込め数リスト[i] > 0:
                production_time = production / (サイクルタイムリスト[i] * 込め数リスト[i])
                daily_time += production_time + setup_time
            
            # 在庫更新
            inventory[i] += production - 出荷数リスト[i]
            
            # 強化された出荷遅れペナルティ
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                # 期間が後になるほど重いペナルティ + 不足量に比例
                delay_penalty = 50000 * shortage_amount * (t + 1)
                total_shortage_penalty += delay_penalty
                inventory[i] = 0
        
        # 時間制約違反に対する非常に大きなペナルティ
        if daily_time > max_daily_time:
            overtime = daily_time - max_daily_time
            total_overtime_penalty += 1000000 * overtime
        
        # 段替え制約ペナルティ
        if daily_setup > 5:
            total_setup_penalty += 10000 * (daily_setup - 5)
    
    total_penalty = total_setup_penalty + total_overtime_penalty + total_shortage_penalty
    return total_penalty,
