"""
Simple example script to run MIP production scheduling with CSV data
"""

from mip_production_scheduler import load_data_from_csv
import pandas as pd

def main():
    """Run MIP production scheduling example"""
    
    print("=== MIP Production Scheduling Example ===")
    
    # Load and solve with default parameters
    scheduler = load_data_from_csv("data_ga.csv")
    
    # Show the original CSV data
    print("\nOriginal CSV Data:")
    df = pd.read_csv("data_ga.csv")
    print(df)
    
    # Show input parameters
    product_names = df['part_number'].tolist()
    scheduler.print_input_data(product_names)
    
    # Solve
    print("\n" + "="*50)
    print("SOLVING...")
    print("="*50)
    
    status = scheduler.solve(verbose=False)  # Set verbose=False for cleaner output
    
    if status == "Optimal":
        print(f"✓ Optimal solution found!")
        scheduler.print_solution()
        
        # Additional analysis
        solution = scheduler.get_solution()
        print(f"\nSummary:")
        print(f"- Total profit: {solution['objective_value']:.2f}")
        print(f"- Total setups: {solution['setup_schedule'].sum():.0f}")
        print(f"- Total production: {solution['production_schedule'].sum():.2f}")
        
    else:
        print(f"✗ Could not find optimal solution. Status: {status}")

if __name__ == "__main__":
    main()
